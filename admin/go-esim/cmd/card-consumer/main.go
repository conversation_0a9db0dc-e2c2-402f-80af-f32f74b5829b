package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/Wenpiner/iot-api/internal/config"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rabbitmq/amqp091-go"
	rabbitConf "github.com/wenpiner/rabbitmq-go/conf"
	"github.com/xuri/excelize/v2"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/service"
)

var configFile = flag.String("f", "etc/esim.yaml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c, conf.UseEnv())

	// 初始化数据库连接
	ctx := context.Background()
	db, err := svc.InitDB(ctx, c)
	if err != nil {
		panic(err)
	}
	defer db.Close()

	// 初始化 Redis 连接
	redisClient, err := svc.InitRedis(ctx, c)
	if err != nil {
		panic(err)
	}
	defer redisClient.Close()

	// 创建服务上下文
	svcCtx := svc.NewServiceContext(c, db, redisClient)

	// 启动消费者
	err = svcCtx.RabbitMQ.Register("card_import", rabbitConf.ConsumerConf{
		Exchange: rabbitConf.NewDirectExchange("card_import"),
		Queue:    rabbitConf.NewQueue("card_import"),
		RouteKey: "card_import",
	}, &CardImporConsumer{svcCtx: svcCtx})
	if err != nil {
		panic(err)
	}

	// 启动服务
	sg := service.NewServiceGroup()
	sg.Add(svcCtx.RabbitMQ)
	defer sg.Stop()

	fmt.Printf("Starting consumer...\n")
	sg.Start()

	// 等待停止信号
	// 等待停止信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	<-sigChan
	fmt.Println("接收到停止信号，正在关闭...")
	sg.Stop()
}

// handleCardImport 处理卡片导入任务
func handleCardImport(svcCtx *svc.ServiceContext, msg []byte) error {
	var data map[string]interface{}
	if err := json.Unmarshal(msg, &data); err != nil {
		return err
	}

	taskID := int(data["task_id"].(float64))
	fileName := data["file_name"].(string)
	fileURL := data["file_url"].(string)
	cost := data["cost"].(float64)
	channelID := int64(data["channel_id"].(float64))
	var tags []string
	if data["tags"] != nil {
		tmpTags := data["tags"].([]interface{})
		for _, tag := range tmpTags {
			tags = append(tags, fmt.Sprintf("%v", tag))
		}
	}

	// 1. 更新任务状态为处理中
	err := updateTaskStatus(svcCtx.DB, taskID, 2, nil, nil, nil, nil)
	if err != nil {
		logx.Errorf("更新任务状态失败: %v", err)
		return err
	}

	// TODO: 2. 下载并解析文件
	// 下载文件到本地，并将文件命名为 “任务ID”+fileName 并保存到本地
	fileName = fmt.Sprintf("%d_%s", taskID, fileName)
	filePath := fmt.Sprintf("/tmp/%s", fileName)

	// 创建一个 defer 函数，用于更新任务状态
	var errMsg string
	defer func() {
		if errMsg != "" {
			updateTaskStatus(svcCtx.DB, taskID, 4, nil, nil, nil, &errMsg)
		}
	}()

	// 下载文件
	resp, err := http.Get(fileURL)
	if err != nil {
		logx.Errorf("下载文件失败: %v", err)
		// 下载文件错误，更新状态
		errMsg = "下载文件失败"
		return nil
	}
	defer resp.Body.Close()
	f, err := os.Create(filePath)
	if err != nil {
		logx.Errorf("创建文件失败: %v", err)
		errMsg = "创建文件失败"
		return nil
	}
	defer f.Close()
	_, err = io.Copy(f, resp.Body)
	if err != nil {
		logx.Errorf("复制文件失败: %v", err)
		errMsg = "复制文件失败"
		return nil
	}

	// 使用excelize读取文件,并解析：从第二行开始解析，第一列是ICCID、第二列是添加日期、第三列是MSISDN、第四列是IMSI
	excelFile, err := excelize.OpenFile(filePath)
	if err != nil {
		logx.Errorf("读取文件失败: %v", err)
		errMsg = "读取文件失败"
		return nil
	}
	defer excelFile.Close()
	sheetName := excelFile.GetSheetName(0)
	rows, err := excelFile.GetRows(sheetName)
	if err != nil {
		logx.Errorf("读取文件失败: %v", err)
		errMsg = "读取文件失败"
		return nil
	}

	ctx := context.Background()

	successRecords := 0
	failedRecords := 0

	type Data struct {
		ICCID   string
		AddDate time.Time
		MSISDN  string
		IMSI    string
	}

	totalData := make([]Data, 0)

	for i, row := range rows {
		if i == 0 {
			continue
		}

		// 如果行数小于4，则跳过
		if len(row) < 4 {
			continue
		}

		iccid := row[0]
		if iccid == "" {
			continue
		}
		addDate := row[1]
		msisdn := row[2]
		imsi := row[3]

		addTime, err := time.Parse("2006-01-02 15:04:05", addDate)
		if err != nil {
			addTime = time.Now()
			err = nil
		}

		totalData = append(totalData, Data{
			ICCID:   iccid,
			AddDate: addTime,
			MSISDN:  msisdn,
			IMSI:    imsi,
		})
	}
	totalRecords := len(totalData)
	if totalRecords == 0 {
		errMsg := "文件为空"
		updateTaskStatus(svcCtx.DB, taskID, 3, nil, &totalRecords, nil, &errMsg)
		return nil
	}
	// 更新任务总数
	updateTaskStatus(svcCtx.DB, taskID, 2, nil, &totalRecords, nil, nil)

	// 记录卡导入成功的数据
	// recordData := make([]int64, 0)

	for _, data := range totalData {
		id, err := svcCtx.CardRepo.CreateSimpleCard(ctx, data.ICCID, data.MSISDN, data.IMSI, data.AddDate, channelID, cost, tags, false)
		var errorMsg string
		recordText := data.ICCID
		status := 1
		if err != nil {
			errorMsg = err.Error()
			status = 2
			failedRecords++
		} else {
			successRecords++
			// TODO 改为被购买后再进行成本计入
			// if len(recordData) >= 1000 {
			// 	svcCtx.SendCardImportData(ctx, task.ImportBalanceAddBody{
			// 		TaskID: taskID,
			// 		Datas:  recordData,
			// 		Cost:   cost,
			// 		Type:   "card",
			// 		UserID: uint32(channelID),
			// 	})
			// 	recordData = make([]int64, 0)
			// } else {
			// 	recordData = append(recordData, id)
			// }
		}
		// 如果 recordData 不为空，则发送数据
		// if len(recordData) > 0 {
		// 	svcCtx.SendCardImportData(ctx, task.ImportBalanceAddBody{
		// 		TaskID: taskID,
		// 		Datas:  recordData,
		// 		Cost:   cost,
		// 		Type:   "card",
		// 		UserID: uint32(channelID),
		// 	})
		// }
		// 创建任务日志
		svcCtx.TaskRepo.CreateTaskLog(ctx, taskID, status, int32(id), recordText, errorMsg)
		updateTaskStatus(svcCtx.DB, taskID, 2, &successRecords, &totalRecords, &failedRecords, nil)
	}

	err = updateTaskStatus(svcCtx.DB, taskID, 3, &successRecords, &totalRecords, &failedRecords, nil)
	if err != nil {
		logx.Errorf("更新任务状态失败: %v", err)
		return err
	}

	return nil
}

// updateTaskStatus 更新任务状态
func updateTaskStatus(db *pgxpool.Pool, taskID int, status int, successRecords, totalRecords, failedRecords *int, errorMessage *string) error {
	args := make(pgx.NamedArgs)
	args["id"] = taskID
	args["status"] = status
	args["updated_at"] = time.Now()

	query := `
		UPDATE iot.task SET
			status = @status,
			updated_at = @updated_at
	`

	if successRecords != nil {
		args["success_records"] = *successRecords
		query += ", success_records = @success_records"
	}
	if totalRecords != nil {
		args["total_records"] = *totalRecords
		query += ", total_records = @total_records"
	}
	if failedRecords != nil {
		args["failed_records"] = *failedRecords
		query += ", failed_records = @failed_records"
	}
	if errorMessage != nil {
		args["error_message"] = *errorMessage
		query += ", error_message = @error_message"
	}

	query += " WHERE id = @id"

	_, err := db.Exec(context.Background(), query, args)
	return err
}

type CardImporConsumer struct {
	svcCtx *svc.ServiceContext
}

func (c *CardImporConsumer) Receive(key string, message amqp091.Delivery) error {
	return handleCardImport(c.svcCtx, message.Body)
}

func (c *CardImporConsumer) Exception(key string, err error, message amqp091.Delivery) {
	logx.Errorf("消费失败: %v", err)
}
