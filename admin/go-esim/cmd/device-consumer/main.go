package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"net/http"
	"os"
	"os/signal"
	"strconv"
	"syscall"
	"time"

	"github.com/Wenpiner/iot-api/internal/config"
	"github.com/Wenpiner/iot-api/internal/repo"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rabbitmq/amqp091-go"
	"github.com/shopspring/decimal"
	rabbitConf "github.com/wenpiner/rabbitmq-go/conf"
	"github.com/xuri/excelize/v2"
	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/service"
)

var configFile = flag.String("f", "etc/esim.yaml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c, conf.UseEnv())

	// 初始化数据库连接
	ctx := context.Background()
	db, err := svc.InitDB(ctx, c)
	if err != nil {
		panic(err)
	}
	defer db.Close()

	// 初始化 Redis 连接
	redisClient, err := svc.InitRedis(ctx, c)
	if err != nil {
		panic(err)
	}
	defer redisClient.Close()

	// 创建服务上下文
	svcCtx := svc.NewServiceContext(c, db, redisClient)

	// 启动消费者
	err = svcCtx.RabbitMQ.Register("device_import", rabbitConf.ConsumerConf{
		Exchange: rabbitConf.NewDirectExchange("device_import"),
		Queue:    rabbitConf.NewQueue("device_import"),
		RouteKey: "device_import",
	}, &DeviceImportConsumer{svcCtx: svcCtx})
	if err != nil {
		panic(err)
	}

	// 启动服务
	sg := service.NewServiceGroup()
	sg.Add(svcCtx.RabbitMQ)
	defer sg.Stop()

	fmt.Printf("Starting device consumer...\n")
	sg.Start()

	// 等待停止信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	<-sigChan
	fmt.Println("接收到停止信号，正在关闭...")
	sg.Stop()
}

type DeviceImportData struct {
	TaskID int `json:"task_id"`
	// 文件信息
	FileName string `json:"file_name"`
	FileURL  string `json:"file_url"`
	// 所属通道
	ChannelID int32 `json:"channel_id"`
	// 设备成本
	Cost float64 `json:"cost"`
	// 这批设备的Tag
	Tags []string `json:"tags"`
	// 设备类型
	DeviceType string `json:"device_type"`
	// 设备型号
	Model string `json:"model"`
}

// handleDeviceImport 处理设备厂商导入任务
func handleDeviceImport(svcCtx *svc.ServiceContext, msg []byte) error {
	var data DeviceImportData
	if err := json.Unmarshal(msg, &data); err != nil {
		return err
	}

	taskID := data.TaskID
	fileName := data.FileName
	fileURL := data.FileURL
	cost := data.Cost
	channelID := data.ChannelID
	tags := data.Tags

	// 1. 更新任务状态为处理中
	err := updateTaskStatus(svcCtx.DB, taskID, 2, nil, nil, nil, nil)
	if err != nil {
		logx.Errorf("更新任务状态失败: %v", err)
		return err
	}

	// 下载文件到本地
	fileName = fmt.Sprintf("%d_%s", taskID, fileName)
	filePath := fmt.Sprintf("/tmp/%s", fileName)

	// 创建一个 defer 函数，用于更新任务状态
	var errMsg string
	defer func() {
		if errMsg != "" {
			updateTaskStatus(svcCtx.DB, taskID, 4, nil, nil, nil, &errMsg)
		}
	}()

	// 下载文件
	resp, err := http.Get(fileURL)
	if err != nil {
		logx.Errorf("下载文件失败: %v", err)
		errMsg = "下载文件失败"
		return nil
	}
	defer resp.Body.Close()
	f, err := os.Create(filePath)
	if err != nil {
		logx.Errorf("创建文件失败: %v", err)
		errMsg = "创建文件失败"
		return nil
	}
	defer f.Close()
	_, err = io.Copy(f, resp.Body)
	if err != nil {
		logx.Errorf("复制文件失败: %v", err)
		errMsg = "复制文件失败"
		return nil
	}

	// 使用excelize读取文件
	excelFile, err := excelize.OpenFile(filePath)
	if err != nil {
		logx.Errorf("读取文件失败: %v", err)
		errMsg = "读取文件失败"
		return nil
	}
	defer excelFile.Close()
	sheetName := excelFile.GetSheetName(0)
	rows, err := excelFile.GetRows(sheetName)
	if err != nil {
		logx.Errorf("读取文件失败: %v", err)
		errMsg = "读取文件失败"
		return nil
	}

	ctx := context.Background()
	successRecords := 0
	failedRecords := 0

	totalData := make([]types.DeviceImportData, 0)

	for i, row := range rows {
		if i == 0 {
			continue
		}

		// 如果行数小于7，则跳过
		if len(row) < 7 {
			continue
		}

		name := getArrayValue(row, 0)
		if name == "" {
			continue
		}
		quantity, _ := strconv.Atoi(getArrayValue(row, 5))
		timeStr := getArrayValue(row, 6)
		t := time.Now()
		// 尝试不同的时间格式进行解析
		formats := []string{
			"2006-01-02 15:04:05",
			"2006/01/02 15:04:05",
			"2006-01-02",
			"2006/01/02",
			"02/01/2006",
			"02-01-2006",
		}
		for _, format := range formats {
			if parsed, err := time.Parse(format, timeStr); err == nil {
				t = parsed
				break
			}
		}
		totalData = append(totalData, types.DeviceImportData{
			BoxNumber:    getArrayValue(row, 0),
			PalletNumber: getArrayValue(row, 1),
			ChargerSN:    getArrayValue(row, 2),
			ChargerSN2:   getArrayValue(row, 3),
			RandomCode:   getArrayValue(row, 4),
			Quantity:     quantity,
			PackageTime:  t,
			DeviceNumber: getArrayValue(row, 7),
			ESIM2ID:      getArrayValue(row, 8),
			ESIM3ID:      getArrayValue(row, 9),
			ESIMID:       getArrayValue(row, 10),
			IMEI:         getArrayValue(row, 11),
			IMSI:         getArrayValue(row, 12),
			MAC:          getArrayValue(row, 13),
			MSISDN:       getArrayValue(row, 14),
			CCID:         getArrayValue(row, 15),
			AccessNumber: getArrayValue(row, 16),
			SN:           getArrayValue(row, 17),
			SSID:         getArrayValue(row, 18),
			VirtualICCID: getArrayValue(row, 19),
			WIFIKEY:      getArrayValue(row, 20),
			WIFIMAC:      getArrayValue(row, 21),
			WIFIMAC5G:    getArrayValue(row, 22),
		})
	}

	totalRecords := len(totalData)
	if totalRecords == 0 {
		errMsg := "文件为空"
		updateTaskStatus(svcCtx.DB, taskID, 3, nil, &totalRecords, nil, &errMsg)
		return nil
	}

	// 更新任务总数
	updateTaskStatus(svcCtx.DB, taskID, 2, nil, &totalRecords, nil, nil)

	// 创建设备厂商仓库实例
	deviceRepo := repo.NewDeviceRepo(svcCtx.DB)

	// 记录设备导入成功的数据
	// recordData := make([]int64, 0)
	for _, data := range totalData {

		id, err := deviceRepo.CreateImportDevice(ctx, int64(channelID), &data, tags, decimal.NewFromFloat(cost))
		var errorMsg string
		recordText := data.DeviceNumber
		status := 1
		if err != nil {
			errorMsg = err.Error()
			status = 2
			failedRecords++
		} else {
			successRecords++
			// if len(recordData) >= 1000 {
			// 	svcCtx.SendCardImportData(ctx, task.ImportBalanceAddBody{
			// 		TaskID: taskID,
			// 		Datas:  recordData,
			// 		Cost:   cost,
			// 		Type:   "card",
			// 		UserID: uint32(channelID),
			// 	})
			// 	recordData = make([]int64, 0)
			// } else {
			// 	recordData = append(recordData, id)
			// }
		}

		// 创建任务日志
		svcCtx.TaskRepo.CreateTaskLog(ctx, taskID, status, int32(id), recordText, errorMsg)
		updateTaskStatus(svcCtx.DB, taskID, 2, &successRecords, &totalRecords, &failedRecords, nil)
	}

	err = updateTaskStatus(svcCtx.DB, taskID, 3, &successRecords, &totalRecords, &failedRecords, nil)
	if err != nil {
		logx.Errorf("更新任务状态失败: %v", err)
		return err
	}

	return nil
}

// updateTaskStatus 更新任务状态
func updateTaskStatus(db *pgxpool.Pool, taskID int, status int, successRecords, totalRecords, failedRecords *int, errorMessage *string) error {
	args := make(pgx.NamedArgs)
	args["id"] = taskID
	args["status"] = status
	args["updated_at"] = time.Now()

	query := `
		UPDATE iot.task SET
			status = @status,
			updated_at = @updated_at
	`

	if successRecords != nil {
		args["success_records"] = *successRecords
		query += `, success_records = @success_records`
	}
	if totalRecords != nil {
		args["total_records"] = *totalRecords
		query += `, total_records = @total_records`
	}
	if failedRecords != nil {
		args["failed_records"] = *failedRecords
		query += `, failed_records = @failed_records`
	}
	if errorMessage != nil {
		args["error_message"] = *errorMessage
		query += `, error_message = @error_message`
	}

	query += ` WHERE id = @id`

	_, err := db.Exec(context.Background(), query, args)
	return err
}

type DeviceImportConsumer struct {
	svcCtx *svc.ServiceContext
}

func (c *DeviceImportConsumer) Receive(key string, message amqp091.Delivery) error {
	return handleDeviceImport(c.svcCtx, message.Body)
}

func (c *DeviceImportConsumer) Exception(key string, err error, message amqp091.Delivery) {
	logx.Errorf("处理消息失败: %v, key: %s, message: %s", err, key, string(message.Body))
}

// 获取数组的指定下标数据，如果长度不够则默认返回0
func getArrayValue(arr []string, index int) string {
	if index < 0 || index >= len(arr) {
		return ""
	}
	return arr[index]
}
