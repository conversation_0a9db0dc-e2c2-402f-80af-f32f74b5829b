FROM golang:1.24.0-alpine AS builder

LABEL stage=gobuilder
RUN apk update --no-cache && apk add --no-cache tzdata build-base

WORKDIR /build

COPY . .

RUN go mod download

RUN env CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -p 4 -ldflags "-s -w" -trimpath \
    		-o application \
    		cmd/card-consumer/main.go

FROM alpine:3.21

# 
WORKDIR /app

ENV TZ=Asia/Shanghai
RUN apk update --no-cache && apk add --no-cache tzdata

COPY --from=builder /build/application ./application

COPY --from=builder /build/etc/esim.yaml ./etc/

ENTRYPOINT ["./application", "-f", "etc/esim.yaml"]