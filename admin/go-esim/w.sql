create table iot.cards
(
    id               serial
        constraint cards_pk
            primary key,
    iccid            varchar(20)                                      not null
        constraint cards_pk_2
            unique,
    msisdn           varchar(15)                                      not null,
    imsi             varchar(20)                                      not null,
    created_at       timestamp      default CURRENT_TIMESTAMP         not null,
    status           integer        default 1                         not null,
    tag              varchar(10)[]  default '{}'::character varying[] not null,
    added_date       timestamp                                        not null,
    updated_at       timestamp      default CURRENT_TIMESTAMP         not null,
    lock_status      integer        default 0                         not null,
    activated_at     timestamp      default CURRENT_TIMESTAMP         not null,
    fixed_ip_address inet           default '127.0.0.1'::inet         not null,
    channel_id       integer                                          not null,
    cost_price       numeric(20, 2) default 0                         not null,
    device_id        integer
);

comment on table iot.cards is '卡板';

comment on column iot.cards.id is '自增ID，无需理会';

comment on column iot.cards.iccid is '卡片唯一标识';

comment on column iot.cards.msisdn is '卡片手机号';

comment on column iot.cards.imsi is '国际移动用户识别码';

comment on column iot.cards.created_at is '创建时间';

comment on column iot.cards.status is '卡片状态

1.可测试（运营商给到平台时）
2.已激活（用户已激活）
3.未激活（厂商分配好了设备时）';

comment on column iot.cards.tag is '卡片标签';

comment on column iot.cards.added_date is '添加时间';

comment on column iot.cards.updated_at is '更新时间';

comment on column iot.cards.lock_status is '锁定状态 0:否，1:是';

comment on column iot.cards.activated_at is '激活日期';

comment on column iot.cards.fixed_ip_address is '最后一次IP地址';

comment on column iot.cards.channel_id is '通道ID';

comment on column iot.cards.cost_price is '当前卡片成本价格';

comment on column iot.cards.device_id is '对于的设备ID';

alter table iot.cards
    owner to postgres;

create table iot.devices
(
    id              serial
        primary key,
    box_no          varchar(50)    default ''::character varying         not null,
    random_code     varchar(50),
    package_date    timestamp,
    device_no       varchar(50)                                          not null
        unique,
    imei            varchar(50),
    imsi            varchar(50)    default ''::character varying         not null,
    mac             varchar(50),
    msisdn          varchar(50)    default ''::character varying         not null,
    ccid            varchar(50)    default ''::character varying         not null,
    access_number   varchar(50)    default ''::character varying         not null,
    sn              varchar(50),
    ssid            varchar(50),
    wifi_key        varchar(50),
    wifi_mac        varchar(50),
    wifi_mac_5g     varchar(50)    default ''::character varying         not null,
    created_at      timestamp      default CURRENT_TIMESTAMP,
    updated_at      timestamp      default CURRENT_TIMESTAMP,
    speed_up_link   integer        default 0                             not null,
    speed_down_link integer        default 0                             not null,
    hidden          integer        default 1                             not null,
    tags            varchar(10)[]  default '{}'::character varying(10)[] not null,
    cost_price      numeric(20, 2) default 0                             not null,
    channel_id      integer
);

comment on table iot.devices is '设备表，存储设备的基本信息';

comment on column iot.devices.box_no is '箱号';

comment on column iot.devices.random_code is '随机码';

comment on column iot.devices.package_date is '包装时间';

comment on column iot.devices.device_no is '设备号';

comment on column iot.devices.imei is '设备IMEI号';

comment on column iot.devices.imsi is '设备IMSI号';

comment on column iot.devices.mac is '设备MAC地址';

comment on column iot.devices.msisdn is '设备手机号';

comment on column iot.devices.ccid is '设备的CCID';

comment on column iot.devices.access_number is '接入号';

comment on column iot.devices.sn is '设备序列号';

comment on column iot.devices.ssid is '设备无线网络名称';

comment on column iot.devices.wifi_key is '设备无线网络密码';

comment on column iot.devices.wifi_mac is '设备2.4G无线MAC地址';

comment on column iot.devices.wifi_mac_5g is '设备5G无线MAC地址';

comment on column iot.devices.created_at is '记录创建时间';

comment on column iot.devices.updated_at is '记录更新时间';

comment on column iot.devices.speed_up_link is '当前上行控速 0 不限速 单位 byte(重置 设备仍生效)';

comment on column iot.devices.speed_down_link is '当前下行控速 0 不限速 单位 byte(重置 设备仍生效)';

comment on column iot.devices.hidden is '设置 WIFI 状态 0 表示关闭，1 表示打开， 2 表示隐藏 SSID(当前 wifi 是打开状态)';

comment on column iot.devices.tags is '设备标签';

comment on column iot.devices.cost_price is '当前设备成本';

alter table iot.devices
    owner to postgres;

create table iot.device_card_bindings
(
    id         serial
        primary key,
    device_id  integer                             not null,
    card_id    integer                             not null,
    is_active  boolean   default false             not null,
    bind_date  timestamp default CURRENT_TIMESTAMP not null,
    created_at timestamp default CURRENT_TIMESTAMP,
    updated_at timestamp default CURRENT_TIMESTAMP
);

comment on table iot.device_card_bindings is '设备与卡片绑定表，存储设备和卡片的绑定关系';

comment on column iot.device_card_bindings.device_id is '设备ID，关联devices表';

comment on column iot.device_card_bindings.card_id is '卡片ID，关联cards表';

comment on column iot.device_card_bindings.is_active is '是否为当前激活的卡片';

comment on column iot.device_card_bindings.bind_date is '卡片绑定时间';

comment on column iot.device_card_bindings.created_at is '记录创建时间';

comment on column iot.device_card_bindings.updated_at is '记录更新时间';

alter table iot.device_card_bindings
    owner to postgres;

create table iot.device_manufacturer
(
    id             serial
        constraint device_manufacturer_pk
            primary key,
    name           varchar(50)                         not null,
    contact_person varchar(50),
    contact_phone  varchar(11),
    contact_email  varchar(20),
    address        text,
    bank_account   varchar(50),
    bank_name      varchar(100),
    created_at     timestamp default CURRENT_TIMESTAMP not null,
    updated_at     timestamp default CURRENT_TIMESTAMP not null
);

comment on table iot.device_manufacturer is '设备生产厂家';

comment on column iot.device_manufacturer.id is '厂家ID';

comment on column iot.device_manufacturer.name is '厂家名称';

comment on column iot.device_manufacturer.contact_person is '联系人姓名，用于记录厂商的主要联系人。';

comment on column iot.device_manufacturer.contact_phone is '联系人电话，用于联系厂商的主要联系人。';

comment on column iot.device_manufacturer.contact_email is '联系人邮箱，用于联系厂商的主要联系人。';

comment on column iot.device_manufacturer.bank_account is '厂商的银行账户信息，用于财务结算。';

comment on column iot.device_manufacturer.bank_name is '厂商的开户行名称，用于财务结算。';

comment on column iot.device_manufacturer.created_at is '创建时间';

comment on column iot.device_manufacturer.updated_at is '更新时间';

alter table iot.device_manufacturer
    owner to postgres;

create table iot.card_channels
(
    id                 serial
        constraint card_channels_pk
            primary key,
    name               varchar(20)                                        not null,
    region             varchar(20),
    contact_person     varchar(20),
    contact_phone      varchar(20),
    real_name_required boolean        default true                        not null,
    channel_type       varchar(20)    default '运营商'::character varying not null,
    extra_data         jsonb          default '{}'::jsonb                 not null,
    created_at         timestamp      default CURRENT_TIMESTAMP           not null,
    updated_at         timestamp      default CURRENT_TIMESTAMP,
    real_name_url      varchar(100),
    balance            numeric(40, 2) default 0                           not null,
    operator           varchar(10)                                        not null
);

comment on column iot.card_channels.id is '通道ID';

comment on column iot.card_channels.name is '通道名称（如移动、联通、电信、虚拟运营商）';

comment on column iot.card_channels.region is '通道所属地区（如广东移动、新疆联通）';

comment on column iot.card_channels.contact_person is '联系人姓名';

comment on column iot.card_channels.contact_phone is '联系人电话 ';

comment on column iot.card_channels.real_name_required is '是否需要实名认证';

comment on column iot.card_channels.channel_type is '通道类型（如运营商、虚拟运营商、国际通道）';

comment on column iot.card_channels.extra_data is '扩展字段';

comment on column iot.card_channels.created_at is '创建时间';

comment on column iot.card_channels.updated_at is '更新时间';

comment on column iot.card_channels.real_name_url is '实名认证地址';

comment on column iot.card_channels.balance is '通道金额';

comment on column iot.card_channels.operator is '所属运营商';

alter table iot.card_channels
    owner to postgres;

create index idx_channels_extra_data
    on iot.card_channels using gin (extra_data);

