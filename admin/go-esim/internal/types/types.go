// Code generated by goctl. DO NOT EDIT.
package types

// The basic response with data | 基础带数据信息
// swagger:model BaseDataInfo
type BaseDataInfo struct {
	// Error code | 错误代码
	Code int `json:"code"`
	// Message | 提示信息
	Msg string `json:"msg"`
	// Data | 数据
	Data string `json:"data,omitempty"`
}

// The basic response with data | 基础带数据信息
// swagger:model BaseListInfo
type BaseListInfo struct {
	// The total number of data | 数据总数
	Total uint64 `json:"total"`
	// Data | 数据
	Data string `json:"data,omitempty"`
}

// The basic response without data | 基础不带数据信息
// swagger:model BaseMsgResp
type BaseMsgResp struct {
	// Error code | 错误代码
	Code int `json:"code"`
	// Message | 提示信息
	Msg string `json:"msg"`
}

// The page request parameters | 列表请求参数
// swagger:model PageInfo
type PageInfo struct {
	// Page number | 第几页
	// required : true
	// min : 0
	Page uint64 `json:"page" validate:"required,number,gt=0"`
	// Page size | 单页数据行数
	// required : true
	// max : 100000
	PageSize uint64 `json:"pageSize" validate:"required,number,lt=100000"`
}

// Basic ID request | 基础ID参数请求
// swagger:model IDReq
type IDReq struct {
	// ID
	// Required: true
	Id uint64 `json:"id" validate:"number"`
}

// Basic IDs request | 基础ID数组参数请求
// swagger:model IDsReq
type IDsReq struct {
	// IDs
	// Required: true
	Ids []uint64 `json:"ids"`
}

// Basic ID request | 基础ID地址参数请求
// swagger:model IDPathReq
type IDPathReq struct {
	// ID
	// Required: true
	Id uint64 `path:"id"`
}

// Basic ID request (int32) | 基础ID参数请求 (int32)
// swagger:model IDInt32Req
type IDInt32Req struct {
	// ID
	// Required: true
	Id int32 `json:"id" validate:"number"`
}

// Basic IDs request (int32) | 基础ID数组参数请求 (int32)
// swagger:model IDsInt32Req
type IDsInt32Req struct {
	// IDs
	// Required: true
	Ids []int32 `json:"ids"`
}

// Basic ID request (int32) | 基础ID地址参数请求 (int32)
// swagger:model IDInt32PathReq
type IDInt32PathReq struct {
	// ID
	// Required: true
	Id int32 `path:"id"`
}

// Basic ID request (uint32) | 基础ID参数请求 (uint32)
// swagger:model IDUint32Req
type IDUint32Req struct {
	// ID
	// Required: true
	Id uint32 `json:"id" validate:"number"`
}

// Basic IDs request (uint32) | 基础ID数组参数请求 (uint32)
// swagger:model IDsUint32Req
type IDsUint32Req struct {
	// IDs
	// Required: true
	Ids []uint32 `json:"ids"`
}

// Basic ID request (uint32) | 基础ID地址参数请求 (uint32)
// swagger:model IDUint32PathReq
type IDUint32PathReq struct {
	// ID
	// Required: true
	Id uint32 `path:"id"`
}

// Basic ID request (int64) | 基础ID参数请求 (int64)
// swagger:model IDInt64Req
type IDInt64Req struct {
	// ID
	// Required: true
	Id int64 `json:"id" validate:"number"`
}

// Basic IDs request (int64) | 基础ID数组参数请求 (int64)
// swagger:model IDsInt64Req
type IDsInt64Req struct {
	// IDs
	// Required: true
	Ids []int64 `json:"ids"`
}

// Basic ID request (int64) | 基础ID地址参数请求 (int64)
// swagger:model IDInt64PathReq
type IDInt64PathReq struct {
	// ID
	// Required: true
	Id int64 `path:"id"`
}

// Basic ID request (string) | 基础ID参数请求 (string)
// swagger:model IDStringReq
type IDStringReq struct {
	// ID
	// Required: true
	Id string `json:"id"`
}

// Basic IDs request (string) | 基础ID数组参数请求 (string)
// swagger:model IDsStringReq
type IDsStringReq struct {
	// IDs
	// Required: true
	Ids []string `json:"ids"`
}

// Basic ID request (string) | 基础ID地址参数请求 (string)
// swagger:model IDStringPathReq
type IDStringPathReq struct {
	// ID
	// Required: true
	Id string `path:"id"`
}

// Basic UUID request in path | 基础UUID地址参数请求
// swagger:model UUIDPathReq
type UUIDPathReq struct {
	// ID
	// Required: true
	Id string `path:"id"`
}

// Basic UUID request | 基础UUID参数请求
// swagger:model UUIDReq
type UUIDReq struct {
	// ID
	// required : true
	// max length : 36
	// min length : 36
	Id string `json:"id" validate:"required,len=36"`
}

// Basic UUID array request | 基础UUID数组参数请求
// swagger:model UUIDsReq
type UUIDsReq struct {
	// Ids
	// Required: true
	Ids []string `json:"ids"`
}

// The base ID response data | 基础ID信息
// swagger:model BaseIDInfo
type BaseIDInfo struct {
	// ID
	Id *uint64 `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The base ID response data (int64) | 基础ID信息 (int64)
// swagger:model BaseIDInt64Info
type BaseIDInt64Info struct {
	// ID
	Id *int64 `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The base ID response data (int32) | 基础ID信息 (int32)
// swagger:model BaseIDInt32Info
type BaseIDInt32Info struct {
	// ID
	Id *int32 `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The base ID response data (uint32) | 基础ID信息 (uint32)
// swagger:model BaseIDUint32Info
type BaseIDUint32Info struct {
	// ID
	Id *uint32 `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The base UUID response data | 基础UUID信息
// swagger:model BaseUUIDInfo
type BaseUUIDInfo struct {
	// ID
	Id *string `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// The base ID response data (string) | 基础ID信息 (string)
// swagger:model BaseIDStringInfo
type BaseIDStringInfo struct {
	// ID
	Id *string `json:"id,optional"`
	// Create date | 创建日期
	CreatedAt *int64 `json:"createdAt,optional"`
	// Update date | 更新日期
	UpdatedAt *int64 `json:"updatedAt,optional"`
}

// swagger:model FileInfo
type FileInfo struct {
	// File name | 文件名
	Name string `json:"name"`
	// URL | 文件URL
	URL string `json:"url"`
}

// 卡管理相关
// swagger:model CardListReq
type CardListReq struct {
	Page PageInfo `json:"page"`
	// ICCID
	ICCID *string `json:"iccid,optional"`
	// 卡片状态
	Status *int32 `json:"status,optional"`
	// 卡片标签
	Tags []string `json:"tags,optional"`
	// 锁定状态
	Locked *bool `json:"locked,optional"`
	// 所属运营商
	Channel *int32 `json:"channel,optional"`
	// 添加时间范围
	AddedDateRange []*int64 `json:"addedDateRange,optional"`
	// 创建时间范围
	CreatedAtRange []*int64 `json:"createdAtRange,optional"`
}

// swagger:model CardChannelInfo
type CardChannelInfo struct {
	// 渠道ID
	ID int64 `json:"id"`
	// 渠道名称
	Name string `json:"name"`
	// 渠道运营商
	Operator string `json:"operator"`
	// real_name_required
	RealNameRequired bool `json:"realNameRequired"`
}

// swagger:model CardInfo
type CardInfo struct {
	// ID
	ID int64 `json:"id"`
	// ICCID
	ICCID string `json:"iccid"`
	// MSISDN
	MSISDN string `json:"msisdn"`
	// 卡片状态
	IMSI string `json:"imsi"`
	// 创建时间
	CreatedAt int64 `json:"createdAt"`
	// 导入时间
	AddedDate int64 `json:"addedDate"`
	// 所属标签
	Tags []string `json:"tags"`
	// 锁定状态
	Locked bool `json:"locked"`
	// 激活日期
	ActivatedAt int64 `json:"activatedAt"`
	// 卡片状态
	Status int32 `json:"status"`
	// 渠道相关
	Channel CardChannelInfo `json:"channel"`
}

// swagger:model CardListInfo
type CardListInfo struct {
	BaseListInfo
	Data []CardInfo `json:"data"`
}

// swagger:model CardListResp
type CardListResp struct {
	BaseMsgResp
	Data CardListInfo `json:"data"`
}

// swagger:model UploadCardFileResp
type UploadCardFileResp struct {
	BaseMsgResp
}

// swagger:model ImportCardReq
type ImportCardReq struct {
	// 文件信息
	File FileInfo `json:"file"`
	// 卡片成本
	Cost float64 `json:"cost"`
	// 所属通道
	Channel int32 `json:"channel"`
	// 这批卡的Tag
	Tags []string `json:"tags,optional"`
}

// swagger:model ChannelCreateReq
type ChannelCreateReq struct {
	// 通道ID
	ID int64 `json:"id,optional"`
	// 通道名称
	Name string `json:"name"`
	// 所属地区
	Region string `json:"region,optional"`
	// 通道类型
	ChannelType string `json:"channelType"`
	// 所属运营商
	Operator string `json:"operator"`
	// 联系人
	ContactPerson string `json:"contactPerson,optional"`
	// 联系电话
	ContactPhone string `json:"contactPhone,optional"`
	// 是否需要实名认证
	RealNameRequired bool `json:"realNameRequired"`
	// 实名认证地址
	RealNameUrl string `json:"realNameUrl,optional"`
	// 通道余额
	Balance float64 `json:"balance,optional"`
	// 扩展数据
	ExtData string `json:"extData,optional"`
}

// swagger:model ChannelCreateResp
type ChannelCreateResp struct {
	BaseMsgResp
}

// swagger:model ChannelListReq
type ChannelListReq struct {
	Page PageInfo `json:"page"`
	// 通道名称
	Name *string `json:"name,optional"`
	// 所属地区
	Region *string `json:"region,optional"`
	// 通道类型
	ChannelType *string `json:"channelType,optional"`
	// 所属运营商
	Operator *string `json:"operator,optional"`
	// 实名认证
	RealNameRequired *bool `json:"realNameRequired,optional"`
}

type ChannelListItem struct {
	// ID
	ID int64 `json:"id"`
	// 通道名称
	Name string `json:"name"`
	// 所属地区
	Region string `json:"region"`
	// 通道类型
	ChannelType string `json:"channelType"`
	// 所属运营商
	Operator string `json:"operator"`
	// 联系人
	ContactPerson string `json:"contactPerson"`
	// 联系电话
	ContactPhone string `json:"contactPhone"`
	// 通道余额
	Balance float64 `json:"balance"`
	// 是否需要实名认证
	RealNameRequired bool `json:"realNameRequired"`
	// 实名认证地址
	RealNameUrl string `json:"realNameUrl"`
	// 扩展数据
	ExtData string `json:"extData"`
	// 创建时间
	CreatedAt int64 `json:"createdAt"`
}

// swagger:model ChannelListInfo
type ChannelListInfo struct {
	BaseListInfo
	Data []ChannelListItem `json:"data"`
}

// swagger:model ChannelListResp
type ChannelListResp struct {
	BaseMsgResp
	Data ChannelListInfo `json:"data"`
}

// swagger:model ImportRecordReq
type ImportRecordReq struct {
	Page PageInfo `json:"page"`
	// 通道ID
	ChannelID *int64 `json:"channelId,optional"`
}

// swagger:model TaskListReq
type TaskListReq struct {
	Page   PageInfo `json:"page"`
	Type   *int32   `json:"type,optional"`
	Status *int32   `json:"status,optional"`
}

type TaskListItem struct {
	ID             int64  `json:"id"`
	Type           int32  `json:"type"`
	OperatorId     string `json:"operatorId"`
	Status         int32  `json:"status"`
	SuccessRecords int64  `json:"successRecords"`
	TotalRecords   int64  `json:"totalRecords"`
	FailedRecords  int64  `json:"failedRecords"`
	ErrorMessage   string `json:"errorMessage"`
	CreatedAt      int64  `json:"createdAt"`
	UpdatedAt      int64  `json:"updatedAt"`
	ExtData        string `json:"extData"`
}

// swagger:model TaskListInfo
type TaskListInfo struct {
	BaseListInfo
	Data []TaskListItem `json:"data"`
}

// swagger:model TaskListResp
type TaskListResp struct {
	BaseMsgResp
	Data TaskListInfo `json:"data"`
}

// swagger:model TaskLogReq
type TaskLogReq struct {
	TaskID int64    `json:"taskId"`
	Page   PageInfo `json:"page"`
	Status *int32   `json:"status,optional"`
}

// swagger:model TaskLogInfo
type TaskLogInfo struct {
	ID         int64  `json:"id"`
	TaskID     int64  `json:"taskId"`
	RecordId   int32  `json:"recordId"`
	RecordText string `json:"recordText"`
	Status     int32  `json:"status"`
	CreatedAt  int64  `json:"createdAt"`
	ErrorText  string `json:"errorText"`
}

// swagger:model TaskLogListInfo
type TaskLogListInfo struct {
	BaseListInfo
	Data []TaskLogInfo `json:"data"`
}

// swagger:model TaskLogResp
type TaskLogResp struct {
	BaseMsgResp
	Data TaskLogListInfo `json:"data"`
}

// swagger:model ImportDeviceReq
type ImportDeviceReq struct {
	// 文件信息
	File FileInfo `json:"file"`
	// 所属通道
	Channel int32 `json:"channel"`
	// 设备成本
	Cost float64 `json:"cost"`
	// 这批设备的Tag
	Tags []string `json:"tags,optional"`
	// 设备类型
	DeviceType string `json:"deviceType"`
	// 设备型号
	Model string `json:"model"`
}

// swagger:model ImportDeviceResp
type ImportDeviceResp struct {
	BaseMsgResp
}

// swagger:model ManufacturerInfo
type ManufacturerInfo struct {
	// ID
	ID int64 `json:"id"`
	// 厂家名称
	Name string `json:"name"`
	// 联系人姓名
	ContactPerson string `json:"contactPerson"`
	// 联系人电话
	ContactPhone string `json:"contactPhone"`
	// 联系人邮箱
	ContactEmail string `json:"contactEmail"`
	// 地址
	Address string `json:"address"`
	// 银行账户
	BankAccount string `json:"bankAccount"`
	// 开户行名称
	BankName string `json:"bankName"`
	// 创建时间
	CreatedAt int64 `json:"createdAt"`
	// 更新时间
	UpdatedAt int64 `json:"updatedAt"`
}

// swagger:model ManufacturerCreateReq
type ManufacturerCreateReq struct {
	// 厂家ID（修改时必填）
	ID *int64 `json:"id,optional"`
	// 厂家名称
	Name string `json:"name"`
	// 联系人姓名
	ContactPerson string `json:"contactPerson,optional"`
	// 联系人电话
	ContactPhone string `json:"contactPhone,optional"`
	// 联系人邮箱
	ContactEmail string `json:"contactEmail,optional"`
	// 地址
	Address string `json:"address,optional"`
	// 银行账户
	BankAccount string `json:"bankAccount,optional"`
	// 开户行名称
	BankName string `json:"bankName,optional"`
}

// swagger:model ManufacturerCreateResp
type ManufacturerCreateResp struct {
	BaseMsgResp
}

// swagger:model ManufacturerListReq
type ManufacturerListReq struct {
	Page PageInfo `json:"page"`
	// 厂家名称
	Name *string `json:"name,optional"`
	// 联系人
	ContactPerson *string `json:"contactPerson,optional"`
	// 联系电话
	ContactPhone *string `json:"contactPhone,optional"`
}

// swagger:model ManufacturerListInfo
type ManufacturerListInfo struct {
	BaseListInfo
	Data []ManufacturerInfo `json:"data"`
}

// swagger:model ManufacturerListResp
type ManufacturerListResp struct {
	BaseMsgResp
	Data ManufacturerListInfo `json:"data"`
}

type Warehouse struct {
	Id            int64  `json:"id"`            // 云仓ID，主键
	WarehouseCode string `json:"warehouseCode"` // 云仓编码，唯一标识
	WarehouseName string `json:"warehouseName"` // 云仓名称
	CompanyName   string `json:"companyName"`   // 云仓公司名称
	ContactPerson string `json:"contactPerson"` // 联系人姓名
	ContactPhone  string `json:"contactPhone"`  // 联系电话
	ContactEmail  string `json:"contactEmail"`  // 联系邮箱
	Address       string `json:"address"`       // 云仓详细地址
	Province      string `json:"province"`      // 省份
	City          string `json:"city"`          // 城市
	District      string `json:"district"`      // 区县
	ApiEndpoint   string `json:"apiEndpoint"`   // 奇门API接口地址
	AppKey        string `json:"appKey"`        // 奇门API应用密钥
	CustomerID    string `json:"customerId"`    // 奇门API客户ID
	Status        int32  `json:"status"`        // 云仓状态：1-正常 2-停用
	CreatedAt     int64  `json:"createdAt"`     // 创建时间
	UpdatedAt     int64  `json:"updatedAt"`     // 更新时间
}

// swagger:model GetWarehouseListReq
type GetWarehouseListReq struct {
	Page     int64  `form:"page,default=1"`      // 页码，默认第1页
	PageSize int64  `form:"pageSize,default=20"` // 每页数量，默认20条
	Keyword  string `form:"keyword,optional"`    // 搜索关键词，可选，支持云仓名称、编码搜索
	Status   int32  `form:"status,optional"`     // 状态筛选，可选：1-正常 2-停用
}

type WarehouseListData struct {
	BaseListInfo
	Data []Warehouse `json:"data"` // 云仓列表
}

// swagger:model GetWarehouseListResp
type GetWarehouseListResp struct {
	BaseMsgResp
	Data WarehouseListData `json:"data"` // 云仓列表数据
}

// swagger:model GetWarehouseDetailReq
type GetWarehouseDetailReq struct {
	Id int64 `path:"id"` // 云仓ID，路径参数
}

// swagger:model GetWarehouseDetailResp
type GetWarehouseDetailResp struct {
	BaseMsgResp
	Data Warehouse `json:"data"` // 云仓详细信息
}

// swagger:model CreateWarehouseReq
type CreateWarehouseReq struct {
	WarehouseCode string `json:"warehouseCode"`         // 云仓编码，必填，全局唯一
	WarehouseName string `json:"warehouseName"`         // 云仓名称，必填
	CompanyName   string `json:"companyName"`           // 云仓公司名称，必填
	ContactPerson string `json:"contactPerson"`         // 联系人姓名，必填
	ContactPhone  string `json:"contactPhone"`          // 联系电话，必填
	ContactEmail  string `json:"contactEmail,optional"` // 联系邮箱，可选
	Address       string `json:"address"`               // 云仓详细地址，必填
	Province      string `json:"province"`              // 省份，必填
	City          string `json:"city"`                  // 城市，必填
	District      string `json:"district,optional"`     // 区县，可选
	ApiEndpoint   string `json:"apiEndpoint"`           // 奇门API接口地址，必填
	AppKey        string `json:"appKey"`                // 奇门API应用密钥，必填
	AppSecret     string `json:"appSecret"`             // 奇门API应用密钥，必填
	CustomerID    string `json:"customerId"`            // 奇门API客户ID，必填
}

// swagger:model CreateWarehouseResp
type CreateWarehouseResp struct {
	BaseMsgResp
	Data CreateWarehouseData `json:"data"` // 创建成功返回的数据
}

type CreateWarehouseData struct {
	Id int64 `json:"id"` // 新创建的云仓ID
}

// swagger:model UpdateWarehouseReq
type UpdateWarehouseReq struct {
	Id            int64  `json:"id"`                     // 云仓ID，必填
	WarehouseName string `json:"warehouseName,optional"` // 云仓名称，可选
	CompanyName   string `json:"companyName,optional"`   // 云仓公司名称，可选
	ContactPerson string `json:"contactPerson,optional"` // 联系人姓名，可选
	ContactPhone  string `json:"contactPhone,optional"`  // 联系电话，可选
	ContactEmail  string `json:"contactEmail,optional"`  // 联系邮箱，可选
	Address       string `json:"address,optional"`       // 云仓详细地址，可选
	Province      string `json:"province,optional"`      // 省份，可选
	City          string `json:"city,optional"`          // 城市，可选
	District      string `json:"district,optional"`      // 区县，可选
	ApiEndpoint   string `json:"apiEndpoint,optional"`   // 奇门API接口地址，可选
	AppKey        string `json:"appKey,optional"`        // 奇门API应用密钥，可选
	AppSecret     string `json:"appSecret,optional"`     // 奇门API应用密钥，可选
	CustomerID    string `json:"customerId,optional"`    // 奇门API客户ID，可选
	Status        int32  `json:"status,optional"`        // 云仓状态，可选：1-正常 2-停用
}

// swagger:model UpdateWarehouseResp
type UpdateWarehouseResp struct {
	BaseMsgResp
}

// =================== 入库管理 ===================
type EntryOrder struct {
	Id                int64            `json:"id"`                   // 入库单ID，主键
	EntryOrderCode    string           `json:"entryOrderCode"`       // 入库单号，唯一标识
	WarehouseCode     string           `json:"warehouseCode"`        // 云仓编码
	WarehouseName     string           `json:"warehouseName"`        // 云仓名称
	OwnerCode         string           `json:"ownerCode"`            // 货主编码，默认NIUYI
	OrderType         string           `json:"orderType"`            // 业务类型：SCRK-生产入库 CGRK-采购入库 DBRK-调拨入库
	PurchaseOrderCode string           `json:"purchaseOrderCode"`    // 采购单号，当orderType=CGRK时使用
	ExpectStartTime   string           `json:"expectStartTime"`      // 预期到货开始时间
	ExpectEndTime     string           `json:"expectEndTime"`        // 预期到货结束时间
	ActualArrivalTime string           `json:"actualArrivalTime"`    // 实际到货时间
	LogisticsCode     string           `json:"logisticsCode"`        // 物流公司编码：SF-顺丰 YTO-圆通等
	LogisticsName     string           `json:"logisticsName"`        // 物流公司名称
	ExpressCode       string           `json:"expressCode"`          // 运单号
	SupplierCode      string           `json:"supplierCode"`         // 供应商编码（设备厂商编码）
	SupplierName      string           `json:"supplierName"`         // 供应商名称（设备厂商名称）
	OperatorCode      string           `json:"operatorCode"`         // 操作员编码
	OperatorName      string           `json:"operatorName"`         // 操作员姓名
	OperateTime       string           `json:"operateTime"`          // 操作时间
	TotalOrderLines   int32            `json:"totalOrderLines"`      // 入库单行数统计
	ExpectedQuantity  int32            `json:"expectedQuantity"`     // 预期入库数量
	ActualQuantity    int32            `json:"actualQuantity"`       // 实际入库数量
	OrderStatus       int32            `json:"orderStatus"`          // 订单状态：1-待入库 2-部分入库 3-全部入库 4-异常 5-取消
	Remark            string           `json:"remark"`               // 备注信息
	QimenEntryOrderID string           `json:"qimenEntryOrderId"`    // 奇门系统返回的入库单ID
	ApiSyncStatus     int32            `json:"apiSyncStatus"`        // API同步状态：0-未同步 1-已同步 2-同步失败
	ApiSyncTime       string           `json:"apiSyncTime"`          // API同步时间
	ApiErrorMessage   string           `json:"apiErrorMessage"`      // API同步错误信息
	CreatedAt         int64            `json:"createdAt"`            // 创建时间
	UpdatedAt         int64            `json:"updatedAt"`            // 更新时间
	OrderLines        []EntryOrderLine `json:"orderLines,omitempty"` // 入库单明细列表，可选
}

type EntryOrderLine struct {
	Id             int64    `json:"id"`             // 入库单明细ID，主键
	EntryOrderID   int64    `json:"entryOrderId"`   // 入库单ID，外键
	EntryOrderCode string   `json:"entryOrderCode"` // 入库单号
	OrderLineNo    string   `json:"orderLineNo"`    // 入库单行号
	OutBizCode     string   `json:"outBizCode"`     // 外部业务编码，用于去重
	OwnerCode      string   `json:"ownerCode"`      // 货主编码
	ItemCode       string   `json:"itemCode"`       // 商品编码（设备序列号）
	ItemID         string   `json:"itemId"`         // 仓储系统商品ID
	ItemName       string   `json:"itemName"`       // 商品名称（设备名称）
	SkuProperty    string   `json:"skuProperty"`    // 商品属性
	PlanQty        int32    `json:"planQty"`        // 计划入库数量
	ActualQty      int32    `json:"actualQty"`      // 实际入库数量
	PurchasePrice  float64  `json:"purchasePrice"`  // 采购价格
	RetailPrice    float64  `json:"retailPrice"`    // 零售价格
	InventoryType  string   `json:"inventoryType"`  // 库存类型：ZP-正品 CC-残次 JS-机损 XS-箱损
	BatchCode      string   `json:"batchCode"`      // 批次编码
	ProduceCode    string   `json:"produceCode"`    // 生产批号
	ProductDate    string   `json:"productDate"`    // 商品生产日期 YYYY-MM-DD
	ExpireDate     string   `json:"expireDate"`     // 商品过期日期 YYYY-MM-DD
	BoxNumber      string   `json:"boxNumber"`      // 箱号
	PalletNumber   string   `json:"palletNumber"`   // 卡板号
	Unit           string   `json:"unit"`           // 单位：台/个/盒/箱等
	SnCodes        []string `json:"snCodes"`        // SN编码列表
	ShelfLocation  string   `json:"shelfLocation"`  // 上架位置
	InboundTime    string   `json:"inboundTime"`    // 实际入库时间
	DeviceStatus   int32    `json:"deviceStatus"`   // 设备状态：1-正常 2-损坏 3-缺失配件
	Remark         string   `json:"remark"`         // 备注
	CreatedAt      int64    `json:"createdAt"`      // 创建时间
}

// swagger:model CreateEntryOrderReq
type CreateEntryOrderReq struct {
	WarehouseCode     string                    `json:"warehouseCode"`              // 云仓编码，必填
	OrderType         string                    `json:"orderType,default=CGRK"`     // 业务类型，默认采购入库
	PurchaseOrderCode string                    `json:"purchaseOrderCode,optional"` // 采购单号，可选
	ExpectStartTime   string                    `json:"expectStartTime,optional"`   // 预期到货开始时间，可选
	ExpectEndTime     string                    `json:"expectEndTime,optional"`     // 预期到货结束时间，可选
	LogisticsCode     string                    `json:"logisticsCode,optional"`     // 物流公司编码，可选
	LogisticsName     string                    `json:"logisticsName,optional"`     // 物流公司名称，可选
	ExpressCode       string                    `json:"expressCode,optional"`       // 运单号，可选
	SupplierCode      string                    `json:"supplierCode"`               // 供应商编码，必填
	SupplierName      string                    `json:"supplierName"`               // 供应商名称，必填
	OperatorName      string                    `json:"operatorName,optional"`      // 操作员姓名，可选
	Remark            string                    `json:"remark,optional"`            // 备注，可选
	OrderLines        []CreateEntryOrderLineReq `json:"orderLines"`                 // 入库单明细列表，必填
}

// swagger:model CreateEntryOrderLineReq
type CreateEntryOrderLineReq struct {
	ItemCode      string   `json:"itemCode"`               // 商品编码（设备序列号），必填
	ItemName      string   `json:"itemName"`               // 商品名称，必填
	PlanQty       int32    `json:"planQty,default=1"`      // 计划入库数量，默认1
	PurchasePrice float64  `json:"purchasePrice,optional"` // 采购价格，可选
	RetailPrice   float64  `json:"retailPrice,optional"`   // 零售价格，可选
	BatchCode     string   `json:"batchCode,optional"`     // 批次编码，可选
	ProduceCode   string   `json:"produceCode,optional"`   // 生产批号，可选
	ProductDate   string   `json:"productDate,optional"`   // 生产日期，可选
	ExpireDate    string   `json:"expireDate,optional"`    // 过期日期，可选
	BoxNumber     string   `json:"boxNumber,optional"`     // 箱号，可选
	PalletNumber  string   `json:"palletNumber,optional"`  // 卡板号，可选
	SnCodes       []string `json:"snCodes,optional"`       // SN编码列表，可选
	Remark        string   `json:"remark,optional"`        // 备注，可选
}

// swagger:model CreateEntryOrderResp
type CreateEntryOrderResp struct {
	BaseMsgResp
	Data CreateEntryOrderData `json:"data"` // 创建成功返回的数据
}

type CreateEntryOrderData struct {
	Id             int64  `json:"id"`             // 新创建的入库单ID
	EntryOrderCode string `json:"entryOrderCode"` // 生成的入库单号
}

// swagger:model GetEntryOrderListReq
type GetEntryOrderListReq struct {
	Page          int64  `form:"page,default=1"`         // 页码，默认第1页
	PageSize      int64  `form:"pageSize,default=20"`    // 每页数量，默认20条
	WarehouseCode string `form:"warehouseCode,optional"` // 云仓编码筛选，可选
	OrderStatus   int32  `form:"orderStatus,optional"`   // 订单状态筛选，可选
	SupplierCode  string `form:"supplierCode,optional"`  // 供应商编码筛选，可选
	StartTime     string `form:"startTime,optional"`     // 开始时间筛选，可选
	EndTime       string `form:"endTime,optional"`       // 结束时间筛选，可选
	Keyword       string `form:"keyword,optional"`       // 关键词搜索，可选，支持入库单号、供应商名称
}

// swagger:model GetEntryOrderListResp
type GetEntryOrderListResp struct {
	BaseMsgResp
	Data EntryOrderListData `json:"data"` // 入库单列表数据
}

type EntryOrderListData struct {
	List     []EntryOrder `json:"list"` // 入库单列表
	PageInfo PageInfo     `json:"page"` // 分页信息
}

// swagger:model GetEntryOrderDetailReq
type GetEntryOrderDetailReq struct {
	Id int64 `path:"id"` // 入库单ID，路径参数
}

// swagger:model GetEntryOrderDetailResp
type GetEntryOrderDetailResp struct {
	BaseMsgResp
	Data EntryOrder `json:"data"` // 入库单详细信息，包含明细
}

// swagger:model ConfirmEntryOrderReq
type ConfirmEntryOrderReq struct {
	Id                int64                      `json:"id"`                         // 入库单ID，必填
	ActualArrivalTime string                     `json:"actualArrivalTime,optional"` // 实际到货时间，可选
	OperatorName      string                     `json:"operatorName,optional"`      // 操作员姓名，可选
	Remark            string                     `json:"remark,optional"`            // 确认备注，可选
	OrderLines        []ConfirmEntryOrderLineReq `json:"orderLines,optional"`        // 入库明细确认，可选
}

// swagger:model ConfirmEntryOrderLineReq
type ConfirmEntryOrderLineReq struct {
	Id            int64  `json:"id"`                     // 入库单明细ID，必填
	ActualQty     int32  `json:"actualQty"`              // 实际入库数量，必填
	ShelfLocation string `json:"shelfLocation,optional"` // 上架位置，可选
	DeviceStatus  int32  `json:"deviceStatus,optional"`  // 设备状态，可选
	Remark        string `json:"remark,optional"`        // 明细备注，可选
}

// swagger:model ConfirmEntryOrderResp
type ConfirmEntryOrderResp struct {
	BaseMsgResp
}

// swagger:model CancelEntryOrderReq
type CancelEntryOrderReq struct {
	Id           int64  `json:"id"`                    // 入库单ID，必填
	CancelReason string `json:"cancelReason"`          // 取消原因，必填
	OperatorName string `json:"operatorName,optional"` // 操作员姓名，可选
}

// swagger:model CancelEntryOrderResp
type CancelEntryOrderResp struct {
	BaseMsgResp
}

// swagger:model BatchCreateEntryLinesReq
type BatchCreateEntryLinesReq struct {
	EntryOrderId int64                     `json:"entryOrderId"` // 入库单ID，必填
	OrderLines   []CreateEntryOrderLineReq `json:"orderLines"`   // 批量入库明细，必填
}

// swagger:model BatchCreateEntryLinesResp
type BatchCreateEntryLinesResp struct {
	BaseMsgResp
	Data BatchCreateEntryLinesData `json:"data"` // 批量创建结果
}

type BatchCreateEntryLinesData struct {
	SuccessCount int32    `json:"successCount"`          // 成功创建数量
	FailCount    int32    `json:"failCount"`             // 失败数量
	FailDetails  []string `json:"failDetails,omitempty"` // 失败详情列表
}

// =================== 出库管理 ===================
type DeliveryOrder struct {
	Id                   int64               `json:"id"`                   // 出库单ID，主键
	DeliveryOrderCode    string              `json:"deliveryOrderCode"`    // 出库单号，唯一标识
	WarehouseCode        string              `json:"warehouseCode"`        // 云仓编码
	WarehouseName        string              `json:"warehouseName"`        // 云仓名称
	OwnerCode            string              `json:"ownerCode"`            // 货主编码，默认NIUYI
	OrderType            string              `json:"orderType"`            // 业务类型：JYCK-交易出库 DBCK-调拨出库
	OrderCreateTime      string              `json:"orderCreateTime"`      // 订单创建时间
	Priority             int32               `json:"priority"`             // 优先级：1-普通 2-紧急 3-特急
	ReceiverName         string              `json:"receiverName"`         // 收货人姓名
	ReceiverPhone        string              `json:"receiverPhone"`        // 收货人电话
	ReceiverMobile       string              `json:"receiverMobile"`       // 收货人手机
	ReceiverEmail        string              `json:"receiverEmail"`        // 收货人邮箱
	ReceiverIdType       string              `json:"receiverIdType"`       // 收件人证件类型：1-身份证 2-军官证 3-护照 4-其他
	ReceiverIdNumber     string              `json:"receiverIdNumber"`     // 收件人证件号码
	ReceiverProvince     string              `json:"receiverProvince"`     // 收货省份
	ReceiverCity         string              `json:"receiverCity"`         // 收货城市
	ReceiverArea         string              `json:"receiverArea"`         // 收货区域
	ReceiverTown         string              `json:"receiverTown"`         // 收货村镇
	ReceiverAddress      string              `json:"receiverAddress"`      // 收货详细地址
	ReceiverZipCode      string              `json:"receiverZipCode"`      // 收货邮编
	RecipientType        int32               `json:"recipientType"`        // 收货人类型：1-合伙人 2-代理商 3-用户
	RecipientId          int64               `json:"recipientId"`          // 收货人ID
	TradeOrderCode       string              `json:"tradeOrderCode"`       // 交易订单号
	ExpectStartTime      string              `json:"expectStartTime"`      // 预期发货开始时间
	ExpectEndTime        string              `json:"expectEndTime"`        // 预期发货结束时间
	LogisticsCode        string              `json:"logisticsCode"`        // 物流公司编码
	LogisticsName        string              `json:"logisticsName"`        // 物流公司名称
	ExpressCode          string              `json:"expressCode"`          // 运单号
	ExpressFee           float64             `json:"expressFee"`           // 快递费用
	TotalOrderLines      int32               `json:"totalOrderLines"`      // 出库单行数统计
	RequestedQuantity    int32               `json:"requestedQuantity"`    // 申请出库数量
	ActualQuantity       int32               `json:"actualQuantity"`       // 实际出库数量
	OrderStatus          int32               `json:"orderStatus"`          // 订单状态：1-待出库 2-已出库 3-已发货 4-已收货 5-异常 6-取消
	ShipTime             string              `json:"shipTime"`             // 发货时间
	ReceiveTime          string              `json:"receiveTime"`          // 收货时间
	OperatorCode         string              `json:"operatorCode"`         // 操作员编码
	OperatorName         string              `json:"operatorName"`         // 操作员姓名
	OperateTime          string              `json:"operateTime"`          // 操作时间
	Remark               string              `json:"remark"`               // 备注
	QimenDeliveryOrderID string              `json:"qimenDeliveryOrderId"` // 奇门系统返回的出库单ID
	ApiSyncStatus        int32               `json:"apiSyncStatus"`        // API同步状态：0-未同步 1-已同步 2-同步失败
	ApiSyncTime          string              `json:"apiSyncTime"`          // API同步时间
	ApiErrorMessage      string              `json:"apiErrorMessage"`      // API同步错误信息
	CreatedAt            int64               `json:"createdAt"`            // 创建时间
	UpdatedAt            int64               `json:"updatedAt"`            // 更新时间
	OrderLines           []DeliveryOrderLine `json:"orderLines,omitempty"` // 出库单明细列表，可选
}

type DeliveryOrderLine struct {
	Id                    int64    `json:"id"`                    // 出库单明细ID，主键
	DeliveryOrderID       int64    `json:"deliveryOrderId"`       // 出库单ID，外键
	DeliveryOrderCode     string   `json:"deliveryOrderCode"`     // 出库单号
	OrderLineNo           string   `json:"orderLineNo"`           // 出库单行号
	OutBizCode            string   `json:"outBizCode"`            // 外部业务编码
	OwnerCode             string   `json:"ownerCode"`             // 货主编码
	ItemCode              string   `json:"itemCode"`              // 商品编码（设备序列号）
	ItemID                string   `json:"itemId"`                // 仓储系统商品ID
	ItemName              string   `json:"itemName"`              // 商品名称
	SkuProperty           string   `json:"skuProperty"`           // 商品属性
	PlanQty               int32    `json:"planQty"`               // 计划出库数量
	ActualQty             int32    `json:"actualQty"`             // 实际出库数量
	RetailPrice           float64  `json:"retailPrice"`           // 零售价格
	InventoryType         string   `json:"inventoryType"`         // 库存类型
	BatchCode             string   `json:"batchCode"`             // 批次编码
	ProduceCode           string   `json:"produceCode"`           // 生产批号
	OriginalShelfLocation string   `json:"originalShelfLocation"` // 原货架位置
	OriginalBoxNumber     string   `json:"originalBoxNumber"`     // 原箱号
	OriginalPalletNumber  string   `json:"originalPalletNumber"`  // 原卡板号
	Unit                  string   `json:"unit"`                  // 单位
	SnCodes               []string `json:"snCodes"`               // SN编码列表
	OutboundTime          string   `json:"outboundTime"`          // 实际出库时间
	Remark                string   `json:"remark"`                // 备注
	CreatedAt             int64    `json:"createdAt"`             // 创建时间
}

// 出库相关请求响应结构继续...
// swagger:model CreateDeliveryOrderReq
type CreateDeliveryOrderReq struct {
	WarehouseCode    string                       `json:"warehouseCode"`             // 云仓编码，必填
	OrderType        string                       `json:"orderType,default=JYCK"`    // 业务类型，默认交易出库
	Priority         int32                        `json:"priority,default=1"`        // 优先级，默认普通
	ReceiverName     string                       `json:"receiverName"`              // 收货人姓名，必填
	ReceiverPhone    string                       `json:"receiverPhone"`             // 收货人电话，必填
	ReceiverMobile   string                       `json:"receiverMobile,optional"`   // 收货人手机，可选
	ReceiverEmail    string                       `json:"receiverEmail,optional"`    // 收货人邮箱，可选
	ReceiverIdType   string                       `json:"receiverIdType,optional"`   // 收件人证件类型，可选
	ReceiverIdNumber string                       `json:"receiverIdNumber,optional"` // 收件人证件号码，可选
	ReceiverProvince string                       `json:"receiverProvince"`          // 收货省份，必填
	ReceiverCity     string                       `json:"receiverCity"`              // 收货城市，必填
	ReceiverArea     string                       `json:"receiverArea,optional"`     // 收货区域，可选
	ReceiverTown     string                       `json:"receiverTown,optional"`     // 收货村镇，可选
	ReceiverAddress  string                       `json:"receiverAddress"`           // 收货详细地址，必填
	ReceiverZipCode  string                       `json:"receiverZipCode,optional"`  // 收货邮编，可选
	RecipientType    int32                        `json:"recipientType"`             // 收货人类型，必填：1-合伙人 2-代理商 3-用户
	RecipientId      int64                        `json:"recipientId"`               // 收货人ID，必填
	TradeOrderCode   string                       `json:"tradeOrderCode,optional"`   // 交易订单号，可选
	ExpectStartTime  string                       `json:"expectStartTime,optional"`  // 预期发货开始时间，可选
	ExpectEndTime    string                       `json:"expectEndTime,optional"`    // 预期发货结束时间，可选
	LogisticsCode    string                       `json:"logisticsCode,optional"`    // 物流公司编码，可选
	LogisticsName    string                       `json:"logisticsName,optional"`    // 物流公司名称，可选
	OperatorName     string                       `json:"operatorName,optional"`     // 操作员姓名，可选
	Remark           string                       `json:"remark,optional"`           // 备注，可选
	OrderLines       []CreateDeliveryOrderLineReq `json:"orderLines"`                // 出库单明细列表，必填
}

// swagger:model CreateDeliveryOrderLineReq
type CreateDeliveryOrderLineReq struct {
	ItemCode  string `json:"itemCode"`           // 商品编码（设备序列号），必填
	ItemName  string `json:"itemName"`           // 商品名称，必填
	PlanQty   int32  `json:"planQty,default=1"`  // 计划出库数量，默认1
	BatchCode string `json:"batchCode,optional"` // 指定批次编码，可选
	Remark    string `json:"remark,optional"`    // 备注，可选
}

// swagger:model CreateDeliveryOrderResp
type CreateDeliveryOrderResp struct {
	BaseMsgResp
	Data CreateDeliveryOrderData `json:"data"` // 创建成功返回的数据
}

type CreateDeliveryOrderData struct {
	Id                int64  `json:"id"`                // 新创建的出库单ID
	DeliveryOrderCode string `json:"deliveryOrderCode"` // 生成的出库单号
}

// =================== 库存管理 ===================
type Inventory struct {
	Id                int64  `json:"id"`                // 库存记录ID，主键
	WarehouseCode     string `json:"warehouseCode"`     // 云仓编码
	OwnerCode         string `json:"ownerCode"`         // 货主编码
	ItemCode          string `json:"itemCode"`          // 商品编码（设备序列号）
	ItemID            string `json:"itemId"`            // 仓储系统商品ID
	ItemName          string `json:"itemName"`          // 商品名称
	AvailableQty      int32  `json:"availableQty"`      // 可用库存数量
	LockedQty         int32  `json:"lockedQty"`         // 锁定库存数量
	PickedQty         int32  `json:"pickedQty"`         // 拣货库存数量
	TotalQty          int32  `json:"totalQty"`          // 总库存数量
	InventoryType     string `json:"inventoryType"`     // 库存类型：ZP-正品 CC-残次等
	InventoryStatus   int32  `json:"inventoryStatus"`   // 库存状态：1-在库 2-预出库 3-已出库 4-损坏 5-丢失
	BatchCode         string `json:"batchCode"`         // 批次编码
	ProduceCode       string `json:"produceCode"`       // 生产批号
	ProductDate       string `json:"productDate"`       // 生产日期
	ExpireDate        string `json:"expireDate"`        // 过期日期
	ShelfLocation     string `json:"shelfLocation"`     // 货架位置
	BoxNumber         string `json:"boxNumber"`         // 箱号
	PalletNumber      string `json:"palletNumber"`      // 卡板号
	InWarehouseTime   string `json:"inWarehouseTime"`   // 入库时间
	OutWarehouseTime  string `json:"outWarehouseTime"`  // 出库时间
	LastInventoryTime string `json:"lastInventoryTime"` // 最后盘点时间
	InBatchNo         string `json:"inBatchNo"`         // 入库批次号
	OutBatchNo        string `json:"outBatchNo"`        // 出库批次号
	Remark            string `json:"remark"`            // 备注
	CreatedAt         int64  `json:"createdAt"`         // 创建时间
	UpdatedAt         int64  `json:"updatedAt"`         // 更新时间
}

// swagger:model QueryInventoryReq
type QueryInventoryReq struct {
	Page            int64  `form:"page,default=1"`           // 页码，默认第1页
	PageSize        int64  `form:"pageSize,default=20"`      // 每页数量，默认20条
	WarehouseCode   string `form:"warehouseCode,optional"`   // 云仓编码筛选，可选
	ItemCode        string `form:"itemCode,optional"`        // 商品编码筛选，可选
	InventoryStatus int32  `form:"inventoryStatus,optional"` // 库存状态筛选，可选
	InventoryType   string `form:"inventoryType,optional"`   // 库存类型筛选，可选
	BatchCode       string `form:"batchCode,optional"`       // 批次编码筛选，可选
	ShelfLocation   string `form:"shelfLocation,optional"`   // 货架位置筛选，可选
	MinQty          int32  `form:"minQty,optional"`          // 最小库存数量筛选，可选
	MaxQty          int32  `form:"maxQty,optional"`          // 最大库存数量筛选，可选
}

// swagger:model QueryInventoryResp
type QueryInventoryResp struct {
	BaseMsgResp
	Data InventoryListData `json:"data"` // 库存查询结果
}

type InventoryListData struct {
	List     []Inventory      `json:"list"`    // 库存列表
	PageInfo PageInfo         `json:"page"`    // 分页信息
	Summary  InventorySummary `json:"summary"` // 库存汇总信息
}

type InventorySummary struct {
	TotalItems   int64 `json:"totalItems"`   // 总商品种类数
	TotalQty     int64 `json:"totalQty"`     // 总库存数量
	AvailableQty int64 `json:"availableQty"` // 总可用库存
	LockedQty    int64 `json:"lockedQty"`    // 总锁定库存
}

// =================== API同步日志 ===================
type QimenSyncLog struct {
	Id               int64  `json:"id"`               // 同步日志ID，主键
	WarehouseCode    string `json:"warehouseCode"`    // 云仓编码
	ApiMethod        string `json:"apiMethod"`        // API方法名
	SyncType         int32  `json:"syncType"`         // 同步类型：1-入库单 2-出库单 3-库存查询 4-库存同步
	SyncDirection    int32  `json:"syncDirection"`    // 同步方向：1-推送到云仓 2-从云仓拉取
	RelatedOrderId   int64  `json:"relatedOrderId"`   // 关联订单ID
	RelatedOrderCode string `json:"relatedOrderCode"` // 关联订单号
	SyncStatus       int32  `json:"syncStatus"`       // 同步状态：1-成功 2-失败 3-部分成功
	ErrorCode        string `json:"errorCode"`        // 错误码
	ErrorMessage     string `json:"errorMessage"`     // 错误信息
	RetryCount       int32  `json:"retryCount"`       // 重试次数
	MaxRetryCount    int32  `json:"maxRetryCount"`    // 最大重试次数
	NextRetryTime    string `json:"nextRetryTime"`    // 下次重试时间
	SyncTime         string `json:"syncTime"`         // 同步时间
	ResponseTime     int32  `json:"responseTime"`     // 响应时间（毫秒）
	CreatedAt        int64  `json:"createdAt"`        // 创建时间
}

// swagger:model GetQimenSyncLogsReq
type GetQimenSyncLogsReq struct {
	Page             int64  `form:"page,default=1"`            // 页码，默认第1页
	PageSize         int64  `form:"pageSize,default=20"`       // 每页数量，默认20条
	WarehouseCode    string `form:"warehouseCode,optional"`    // 云仓编码筛选，可选
	SyncType         int32  `form:"syncType,optional"`         // 同步类型筛选，可选
	SyncStatus       int32  `form:"syncStatus,optional"`       // 同步状态筛选，可选
	StartTime        string `form:"startTime,optional"`        // 开始时间筛选，可选
	EndTime          string `form:"endTime,optional"`          // 结束时间筛选，可选
	RelatedOrderCode string `form:"relatedOrderCode,optional"` // 关联订单号筛选，可选
}

// swagger:model GetQimenSyncLogsResp
type GetQimenSyncLogsResp struct {
	BaseMsgResp
	Data QimenSyncLogListData `json:"data"` // 同步日志列表数据
}

type QimenSyncLogListData struct {
	List     []QimenSyncLog `json:"list"` // 同步日志列表
	PageInfo PageInfo       `json:"page"` // 分页信息
}

// =================== 同步操作请求响应 ===================
// swagger:model SyncEntryOrderToQimenReq
type SyncEntryOrderToQimenReq struct {
	EntryOrderId int64 `json:"entryOrderId"`            // 入库单ID，必填
	ForceSync    bool  `json:"forceSync,default=false"` // 是否强制同步，默认false
}

// swagger:model SyncEntryOrderToQimenResp
type SyncEntryOrderToQimenResp struct {
	BaseMsgResp
	Data SyncResultData `json:"data"` // 同步结果数据
}

type SyncResultData struct {
	SyncLogId    int64  `json:"syncLogId"`              // 同步日志ID
	QimenOrderId string `json:"qimenOrderId,omitempty"` // 奇门系统订单ID
	SyncStatus   int32  `json:"syncStatus"`             // 同步状态
	Message      string `json:"message"`                // 同步结果消息
}

// swagger:model SyncDeliveryOrderToQimenReq
type SyncDeliveryOrderToQimenReq struct {
	DeliveryOrderId int64 `json:"deliveryOrderId"`         // 出库单ID，必填
	ForceSync       bool  `json:"forceSync,default=false"` // 是否强制同步，默认false
}

// swagger:model SyncDeliveryOrderToQimenResp
type SyncDeliveryOrderToQimenResp struct {
	BaseMsgResp
	Data SyncResultData `json:"data"` // 同步结果数据
}

// swagger:model SyncInventoryFromQimenReq
type SyncInventoryFromQimenReq struct {
	WarehouseCode string   `json:"warehouseCode"`      // 云仓编码，必填
	ItemCodes     []string `json:"itemCodes,optional"` // 指定商品编码列表，可选，为空则同步所有
}

// swagger:model SyncInventoryFromQimenResp
type SyncInventoryFromQimenResp struct {
	BaseMsgResp
	Data InventorySyncResultData `json:"data"` // 库存同步结果
}

type InventorySyncResultData struct {
	SyncLogId    int64  `json:"syncLogId"`    // 同步日志ID
	SyncCount    int32  `json:"syncCount"`    // 同步数量
	SuccessCount int32  `json:"successCount"` // 成功数量
	FailCount    int32  `json:"failCount"`    // 失败数量
	Message      string `json:"message"`      // 同步结果消息
}

// swagger:model RetryQimenSyncReq
type RetryQimenSyncReq struct {
	SyncLogIds []int64 `json:"syncLogIds"` // 同步日志ID列表，必填
}

// swagger:model RetryQimenSyncResp
type RetryQimenSyncResp struct {
	BaseMsgResp
	Data RetryResultData `json:"data"` // 重试结果数据
}

type RetryResultData struct {
	TotalCount   int32         `json:"totalCount"`        // 总重试数量
	SuccessCount int32         `json:"successCount"`      // 成功数量
	FailCount    int32         `json:"failCount"`         // 失败数量
	Details      []RetryDetail `json:"details,omitempty"` // 重试详情
}

type RetryDetail struct {
	SyncLogId int64  `json:"syncLogId"` // 同步日志ID
	Success   bool   `json:"success"`   // 是否成功
	Message   string `json:"message"`   // 结果消息
}

// 出库单列表相关
// swagger:model GetDeliveryOrderListReq
type GetDeliveryOrderListReq struct {
	Page          int64  `form:"page,default=1"`         // 页码，默认第1页
	PageSize      int64  `form:"pageSize,default=20"`    // 每页数量，默认20条
	WarehouseCode string `form:"warehouseCode,optional"` // 云仓编码筛选，可选
	OrderStatus   int32  `form:"orderStatus,optional"`   // 订单状态筛选，可选
	RecipientType int32  `form:"recipientType,optional"` // 收货人类型筛选，可选
	StartTime     string `form:"startTime,optional"`     // 开始时间筛选，可选
	EndTime       string `form:"endTime,optional"`       // 结束时间筛选，可选
	Keyword       string `form:"keyword,optional"`       // 关键词搜索，可选，支持出库单号、收货人姓名
}

// swagger:model GetDeliveryOrderListResp
type GetDeliveryOrderListResp struct {
	BaseMsgResp
	Data DeliveryOrderListData `json:"data"` // 出库单列表数据
}

type DeliveryOrderListData struct {
	List     []DeliveryOrder `json:"list"` // 出库单列表
	PageInfo PageInfo        `json:"page"` // 分页信息
}

// swagger:model GetDeliveryOrderDetailReq
type GetDeliveryOrderDetailReq struct {
	Id int64 `path:"id"` // 出库单ID，路径参数
}

// swagger:model GetDeliveryOrderDetailResp
type GetDeliveryOrderDetailResp struct {
	BaseMsgResp
	Data DeliveryOrder `json:"data"` // 出库单详细信息，包含明细
}

// 出库确认相关
// swagger:model ConfirmDeliveryOrderReq
type ConfirmDeliveryOrderReq struct {
	Id           int64                         `json:"id"`                    // 出库单ID，必填
	OperatorName string                        `json:"operatorName,optional"` // 操作员姓名，可选
	Remark       string                        `json:"remark,optional"`       // 确认备注，可选
	OrderLines   []ConfirmDeliveryOrderLineReq `json:"orderLines,optional"`   // 出库明细确认，可选
}

// swagger:model ConfirmDeliveryOrderLineReq
type ConfirmDeliveryOrderLineReq struct {
	Id                    int64  `json:"id"`                             // 出库单明细ID，必填
	ActualQty             int32  `json:"actualQty"`                      // 实际出库数量，必填
	OriginalShelfLocation string `json:"originalShelfLocation,optional"` // 原货架位置，可选
	Remark                string `json:"remark,optional"`                // 明细备注，可选
}

// swagger:model ConfirmDeliveryOrderResp
type ConfirmDeliveryOrderResp struct {
	BaseMsgResp
}

// 发货确认相关
// swagger:model ShipDeliveryOrderReq
type ShipDeliveryOrderReq struct {
	Id            int64   `json:"id"`                    // 出库单ID，必填
	LogisticsCode string  `json:"logisticsCode"`         // 物流公司编码，必填
	LogisticsName string  `json:"logisticsName"`         // 物流公司名称，必填
	ExpressCode   string  `json:"expressCode"`           // 运单号，必填
	ExpressFee    float64 `json:"expressFee,optional"`   // 快递费用，可选
	ShipTime      string  `json:"shipTime,optional"`     // 发货时间，可选，默认当前时间
	OperatorName  string  `json:"operatorName,optional"` // 操作员姓名，可选
	Remark        string  `json:"remark,optional"`       // 发货备注，可选
}

// swagger:model ShipDeliveryOrderResp
type ShipDeliveryOrderResp struct {
	BaseMsgResp
}

// 收货确认相关
// swagger:model ReceiveDeliveryOrderReq
type ReceiveDeliveryOrderReq struct {
	Id           int64  `json:"id"`                    // 出库单ID，必填
	ReceiveTime  string `json:"receiveTime,optional"`  // 收货时间，可选，默认当前时间
	ReceiverName string `json:"receiverName,optional"` // 实际收货人姓名，可选
	Remark       string `json:"remark,optional"`       // 收货备注，可选
}

// swagger:model ReceiveDeliveryOrderResp
type ReceiveDeliveryOrderResp struct {
	BaseMsgResp
}

// 库存相关缺少的类型
// swagger:model InventoryCheckReq
type InventoryCheckReq struct {
	WarehouseCode  string   `json:"warehouseCode"`           // 云仓编码，必填
	CheckType      int32    `json:"checkType,default=1"`     // 盘点类型：1-全盘 2-抽盘 3-循环盘点
	ItemCodes      []string `json:"itemCodes,optional"`      // 指定商品编码列表，可选
	ShelfLocations []string `json:"shelfLocations,optional"` // 指定货架位置列表，可选
	OperatorName   string   `json:"operatorName"`            // 操作员姓名，必填
	Remark         string   `json:"remark,optional"`         // 盘点备注，可选
}

// swagger:model InventoryCheckResp
type InventoryCheckResp struct {
	BaseMsgResp
	Data InventoryCheckData `json:"data"` // 盘点结果数据
}

type InventoryCheckData struct {
	CheckId      int64 `json:"checkId"`      // 盘点任务ID
	TotalItems   int32 `json:"totalItems"`   // 盘点商品总数
	CheckedItems int32 `json:"checkedItems"` // 已盘点商品数
	DiffItems    int32 `json:"diffItems"`    // 差异商品数
	Status       int32 `json:"status"`       // 盘点状态：1-进行中 2-已完成
}

// swagger:model AdjustInventoryReq
type AdjustInventoryReq struct {
	WarehouseCode string `json:"warehouseCode"`      // 云仓编码，必填
	ItemCode      string `json:"itemCode"`           // 商品编码，必填
	AdjustType    int32  `json:"adjustType"`         // 调整类型：1-增加 2-减少
	AdjustQty     int32  `json:"adjustQty"`          // 调整数量，必填
	AdjustReason  string `json:"adjustReason"`       // 调整原因，必填
	BatchCode     string `json:"batchCode,optional"` // 批次编码，可选
	OperatorName  string `json:"operatorName"`       // 操作员姓名，必填
	Remark        string `json:"remark,optional"`    // 调整备注，可选
}

// swagger:model AdjustInventoryResp
type AdjustInventoryResp struct {
	BaseMsgResp
	Data AdjustInventoryData `json:"data"` // 调整结果数据
}

type AdjustInventoryData struct {
	BeforeQty int32 `json:"beforeQty"` // 调整前数量
	AfterQty  int32 `json:"afterQty"`  // 调整后数量
	AdjustQty int32 `json:"adjustQty"` // 调整数量
}

// swagger:model GetInventoryLogsReq
type GetInventoryLogsReq struct {
	Page          int64  `form:"page,default=1"`         // 页码，默认第1页
	PageSize      int64  `form:"pageSize,default=20"`    // 每页数量，默认20条
	WarehouseCode string `form:"warehouseCode,optional"` // 云仓编码筛选，可选
	ItemCode      string `form:"itemCode,optional"`      // 商品编码筛选，可选
	OperationType int32  `form:"operationType,optional"` // 操作类型筛选，可选
	StartTime     string `form:"startTime,optional"`     // 开始时间筛选，可选
	EndTime       string `form:"endTime,optional"`       // 结束时间筛选，可选
}

// swagger:model GetInventoryLogsResp
type GetInventoryLogsResp struct {
	BaseMsgResp
	Data InventoryLogListData `json:"data"` // 库存日志列表数据
}

type InventoryLogListData struct {
	List     []InventoryLog `json:"list"` // 库存日志列表
	PageInfo PageInfo       `json:"page"` // 分页信息
}

type InventoryLog struct {
	Id                int64  `json:"id"`                // 日志ID，主键
	WarehouseCode     string `json:"warehouseCode"`     // 云仓编码
	OwnerCode         string `json:"ownerCode"`         // 货主编码
	ItemCode          string `json:"itemCode"`          // 商品编码
	ItemName          string `json:"itemName"`          // 商品名称
	OperationType     int32  `json:"operationType"`     // 操作类型：1-入库 2-出库 3-盘点 4-调拨 5-报损 6-报溢
	OperationDesc     string `json:"operationDesc"`     // 操作描述
	OperationQuantity int32  `json:"operationQuantity"` // 操作数量
	BeforeQty         int32  `json:"beforeQty"`         // 操作前数量
	AfterQty          int32  `json:"afterQty"`          // 操作后数量
	RelatedOrderId    int64  `json:"relatedOrderId"`    // 关联订单ID
	RelatedOrderCode  string `json:"relatedOrderCode"`  // 关联订单号
	RelatedOrderType  int32  `json:"relatedOrderType"`  // 关联订单类型
	BatchCode         string `json:"batchCode"`         // 批次编码
	InventoryType     string `json:"inventoryType"`     // 库存类型
	OperatorId        int64  `json:"operatorId"`        // 操作员ID
	OperatorName      string `json:"operatorName"`      // 操作员姓名
	OperationTime     string `json:"operationTime"`     // 操作时间
	Remark            string `json:"remark"`            // 备注
	CreatedAt         int64  `json:"createdAt"`         // 创建时间
}
