package types

import "time"

type DeviceImportData struct {
	BoxNumber    string    // 箱号
	PalletNumber string    // 卡板号
	ChargerSN    string    // 充电器SN
	ChargerSN2   string    // 充电器SN2
	RandomCode   string    // 随机码
	Quantity     int       // 数量
	PackageTime  time.Time // 包装时间
	DeviceNumber string    // 设备号
	ESIM2ID      string    // ESIM2ID
	ESIM3ID      string    // ESIM3ID
	ESIMID       string    // ESIMID
	IMEI         string    // IMEI
	IMSI         string    // IMSI
	MAC          string    // MAC
	MSISDN       string    // MSISDN
	CCID         string    // CCID
	AccessNumber string    // 接入号
	SN           string    // SN
	SSID         string    // SSID
	VirtualICCID string    // 虚拟卡ICCID
	WIFIKEY      string    // WIFIKEY
	WIFIMAC      string    // WIFIMAC
	WIFIMAC5G    string    // WIFIMAC5G
}
