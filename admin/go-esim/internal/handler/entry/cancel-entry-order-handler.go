package entry

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"github.com/Wenpiner/iot-api/internal/logic/entry"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
)

// swagger:route put /warehouse/entry/order/cancel entry CancelEntryOrder
//

//

//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: CancelEntryOrderReq
//
// Responses:
//  200: CancelEntryOrderResp

func CancelEntryOrderHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.CancelEntryOrderReq
		if err := httpx.Parse(r, &req, true); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := entry.NewCancelEntryOrderLogic(r.Context(), svcCtx)
		resp, err := l.CancelEntryOrder(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
