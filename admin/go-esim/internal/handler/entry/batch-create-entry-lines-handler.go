package entry

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"github.com/Wenpiner/iot-api/internal/logic/entry"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
)

// swagger:route post /warehouse/entry/lines/batch entry BatchCreateEntryLines
//

//

//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: BatchCreateEntryLinesReq
//
// Responses:
//  200: BatchCreateEntryLinesResp

func BatchCreateEntryLinesHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.BatchCreateEntryLinesReq
		if err := httpx.Parse(r, &req, true); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := entry.NewBatchCreateEntryLinesLogic(r.Context(), svcCtx)
		resp, err := l.BatchCreateEntryLines(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
