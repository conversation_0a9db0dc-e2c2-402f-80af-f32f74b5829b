package entry

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"github.com/Wenpiner/iot-api/internal/logic/entry"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
)

// swagger:route get /warehouse/entry/order/list entry GetEntryOrderList
//

//

//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: GetEntryOrderListReq
//
// Responses:
//  200: GetEntryOrderListResp

func GetEntryOrderListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetEntryOrderListReq
		if err := httpx.Parse(r, &req, true); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := entry.NewGetEntryOrderListLogic(r.Context(), svcCtx)
		resp, err := l.GetEntryOrderList(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
