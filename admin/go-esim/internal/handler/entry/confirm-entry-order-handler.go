package entry

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"github.com/Wenpiner/iot-api/internal/logic/entry"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
)

// swagger:route put /warehouse/entry/order/confirm entry ConfirmEntryOrder
//

//

//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: ConfirmEntryOrderReq
//
// Responses:
//  200: ConfirmEntryOrderResp

func ConfirmEntryOrderHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ConfirmEntryOrderReq
		if err := httpx.Parse(r, &req, true); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := entry.NewConfirmEntryOrderLogic(r.Context(), svcCtx)
		resp, err := l.ConfirmEntryOrder(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
