package warehouse

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"github.com/Wenpiner/iot-api/internal/logic/warehouse"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
)

// swagger:route put /warehouse/update warehouse UpdateWarehouse
//

//

//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: UpdateWarehouseReq
//
// Responses:
//  200: UpdateWarehouseResp

func UpdateWarehouseHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.UpdateWarehouseReq
		if err := httpx.Parse(r, &req, true); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := warehouse.NewUpdateWarehouseLogic(r.Context(), svcCtx)
		resp, err := l.UpdateWarehouse(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
