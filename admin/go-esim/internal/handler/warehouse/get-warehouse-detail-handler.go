package warehouse

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"github.com/Wenpiner/iot-api/internal/logic/warehouse"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
)

// swagger:route get /warehouse/detail/{id} warehouse GetWarehouseDetail
//

//

//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: GetWarehouseDetailReq
//
// Responses:
//  200: GetWarehouseDetailResp

func GetWarehouseDetailHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetWarehouseDetailReq
		if err := httpx.Parse(r, &req, true); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := warehouse.NewGetWarehouseDetailLogic(r.Context(), svcCtx)
		resp, err := l.GetWarehouseDetail(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
