package card

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"github.com/Wenpiner/iot-api/internal/logic/card"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
)

// swagger:route get /card/channel/list card GetChannelList
//
// 获取通道列表
//
// 获取通道列表
//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: ChannelListReq
//
// Responses:
//  200: ChannelListResp

func GetChannelListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ChannelListReq
		if err := httpx.Parse(r, &req, true); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := card.NewGetChannelListLogic(r.Context(), svcCtx)
		resp, err := l.GetChannelList(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
