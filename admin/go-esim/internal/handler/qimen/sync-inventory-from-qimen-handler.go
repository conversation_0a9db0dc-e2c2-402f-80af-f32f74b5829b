package qimen

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"github.com/Wenpiner/iot-api/internal/logic/qimen"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
)

// swagger:route post /warehouse/qimen/sync/inventory qimen SyncInventoryFromQimen
//

//

//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: SyncInventoryFromQimenReq
//
// Responses:
//  200: SyncInventoryFromQimenResp

func SyncInventoryFromQimenHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.SyncInventoryFromQimenReq
		if err := httpx.Parse(r, &req, true); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := qimen.NewSyncInventoryFromQimenLogic(r.Context(), svcCtx)
		resp, err := l.SyncInventoryFromQimen(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
