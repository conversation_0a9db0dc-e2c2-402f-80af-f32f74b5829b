package qimen

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"github.com/Wenpiner/iot-api/internal/logic/qimen"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
)

// swagger:route post /warehouse/qimen/sync/delivery qimen SyncDeliveryOrderToQimen
//

//

//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: SyncDeliveryOrderToQimenReq
//
// Responses:
//  200: SyncDeliveryOrderToQimenResp

func SyncDeliveryOrderToQimenHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.SyncDeliveryOrderToQimenReq
		if err := httpx.Parse(r, &req, true); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := qimen.NewSyncDeliveryOrderToQimenLogic(r.Context(), svcCtx)
		resp, err := l.SyncDeliveryOrderToQimen(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
