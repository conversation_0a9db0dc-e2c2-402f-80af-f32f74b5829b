package qimen

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"github.com/Wenpiner/iot-api/internal/logic/qimen"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
)

// swagger:route get /warehouse/qimen/sync/logs qimen GetQimenSyncLogs
//

//

//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: GetQimenSyncLogsReq
//
// Responses:
//  200: GetQimenSyncLogsResp

func GetQimenSyncLogsHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetQimenSyncLogsReq
		if err := httpx.Parse(r, &req, true); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := qimen.NewGetQimenSyncLogsLogic(r.Context(), svcCtx)
		resp, err := l.GetQimenSyncLogs(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
