// Code generated by goctl. DO NOT EDIT.
// goctls v1.10.12

package handler

import (
	"net/http"

	card "github.com/Wenpiner/iot-api/internal/handler/card"
	delivery "github.com/Wenpiner/iot-api/internal/handler/delivery"
	device "github.com/Wenpiner/iot-api/internal/handler/device"
	entry "github.com/Wenpiner/iot-api/internal/handler/entry"
	inventory "github.com/Wenpiner/iot-api/internal/handler/inventory"
	qimen "github.com/Wenpiner/iot-api/internal/handler/qimen"
	task "github.com/Wenpiner/iot-api/internal/handler/task"
	warehouse "github.com/Wenpiner/iot-api/internal/handler/warehouse"
	"github.com/Wenpiner/iot-api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/list",
					Handler: card.GetCardListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/import",
					Handler: card.ImportCardHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/channel/create",
					Handler: card.CreateChannelHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/channel/list",
					Handler: card.GetChannelListHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/card"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/list",
					Handler: task.GetTaskListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/log",
					Handler: task.GetTaskLogHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/esim-task"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/import",
					Handler: device.ImportDeviceHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/manufacturer/create",
					Handler: device.CreateManufacturerHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/manufacturer/list",
					Handler: device.GetManufacturerListHandler(serverCtx),
				},
			}...,
		),
		rest.WithJwt(serverCtx.Config.Auth.AccessSecret),
		rest.WithPrefix("/device"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// 获取云仓列表
				Method:  http.MethodGet,
				Path:    "/list",
				Handler: warehouse.GetWarehouseListHandler(serverCtx),
			},
			{
				// 获取云仓详情
				Method:  http.MethodGet,
				Path:    "/detail/:id",
				Handler: warehouse.GetWarehouseDetailHandler(serverCtx),
			},
			{
				// 创建云仓
				Method:  http.MethodPost,
				Path:    "/create",
				Handler: warehouse.CreateWarehouseHandler(serverCtx),
			},
			{
				// 更新云仓
				Method:  http.MethodPut,
				Path:    "/update",
				Handler: warehouse.UpdateWarehouseHandler(serverCtx),
			},
		},
		rest.WithPrefix("/warehouse"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// 创建入库单
				Method:  http.MethodPost,
				Path:    "/order/create",
				Handler: entry.CreateEntryOrderHandler(serverCtx),
			},
			{
				// 获取入库单列表
				Method:  http.MethodGet,
				Path:    "/order/list",
				Handler: entry.GetEntryOrderListHandler(serverCtx),
			},
			{
				// 获取入库单详情
				Method:  http.MethodGet,
				Path:    "/order/detail/:id",
				Handler: entry.GetEntryOrderDetailHandler(serverCtx),
			},
			{
				// 确认入库
				Method:  http.MethodPut,
				Path:    "/order/confirm",
				Handler: entry.ConfirmEntryOrderHandler(serverCtx),
			},
			{
				// 取消入库单
				Method:  http.MethodPut,
				Path:    "/order/cancel",
				Handler: entry.CancelEntryOrderHandler(serverCtx),
			},
			{
				// 批量录入入库明细
				Method:  http.MethodPost,
				Path:    "/lines/batch",
				Handler: entry.BatchCreateEntryLinesHandler(serverCtx),
			},
		},
		rest.WithPrefix("/warehouse/entry"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// 创建出库单
				Method:  http.MethodPost,
				Path:    "/order/create",
				Handler: delivery.CreateDeliveryOrderHandler(serverCtx),
			},
			{
				// 获取出库单列表
				Method:  http.MethodGet,
				Path:    "/order/list",
				Handler: delivery.GetDeliveryOrderListHandler(serverCtx),
			},
			{
				// 获取出库单详情
				Method:  http.MethodGet,
				Path:    "/order/detail/:id",
				Handler: delivery.GetDeliveryOrderDetailHandler(serverCtx),
			},
			{
				// 确认出库
				Method:  http.MethodPut,
				Path:    "/order/confirm",
				Handler: delivery.ConfirmDeliveryOrderHandler(serverCtx),
			},
			{
				// 发货确认
				Method:  http.MethodPut,
				Path:    "/order/ship",
				Handler: delivery.ShipDeliveryOrderHandler(serverCtx),
			},
			{
				// 收货确认
				Method:  http.MethodPut,
				Path:    "/order/receive",
				Handler: delivery.ReceiveDeliveryOrderHandler(serverCtx),
			},
		},
		rest.WithPrefix("/warehouse/delivery"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// 查询库存
				Method:  http.MethodGet,
				Path:    "/query",
				Handler: inventory.QueryInventoryHandler(serverCtx),
			},
			{
				// 库存盘点
				Method:  http.MethodPost,
				Path:    "/check",
				Handler: inventory.InventoryCheckHandler(serverCtx),
			},
			{
				// 库存调整
				Method:  http.MethodPost,
				Path:    "/adjust",
				Handler: inventory.AdjustInventoryHandler(serverCtx),
			},
			{
				// 获取库存变动日志
				Method:  http.MethodGet,
				Path:    "/logs",
				Handler: inventory.GetInventoryLogsHandler(serverCtx),
			},
		},
		rest.WithPrefix("/warehouse/inventory"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// 手动同步入库单到奇门
				Method:  http.MethodPost,
				Path:    "/sync/entry",
				Handler: qimen.SyncEntryOrderToQimenHandler(serverCtx),
			},
			{
				// 手动同步出库单到奇门
				Method:  http.MethodPost,
				Path:    "/sync/delivery",
				Handler: qimen.SyncDeliveryOrderToQimenHandler(serverCtx),
			},
			{
				// 从奇门同步库存
				Method:  http.MethodPost,
				Path:    "/sync/inventory",
				Handler: qimen.SyncInventoryFromQimenHandler(serverCtx),
			},
			{
				// 获取API同步日志
				Method:  http.MethodGet,
				Path:    "/sync/logs",
				Handler: qimen.GetQimenSyncLogsHandler(serverCtx),
			},
			{
				// 重试失败的同步任务
				Method:  http.MethodPost,
				Path:    "/sync/retry",
				Handler: qimen.RetryQimenSyncHandler(serverCtx),
			},
		},
		rest.WithPrefix("/warehouse/qimen"),
	)
}
