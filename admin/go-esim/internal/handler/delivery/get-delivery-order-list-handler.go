package delivery

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"github.com/Wenpiner/iot-api/internal/logic/delivery"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
)

// swagger:route get /warehouse/delivery/order/list delivery GetDeliveryOrderList
//

//

//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: GetDeliveryOrderListReq
//
// Responses:
//  200: GetDeliveryOrderListResp

func GetDeliveryOrderListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetDeliveryOrderListReq
		if err := httpx.Parse(r, &req, true); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := delivery.NewGetDeliveryOrderListLogic(r.Context(), svcCtx)
		resp, err := l.GetDeliveryOrderList(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
