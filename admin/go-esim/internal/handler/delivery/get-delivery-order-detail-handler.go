package delivery

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"github.com/Wenpiner/iot-api/internal/logic/delivery"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
)

// swagger:route get /warehouse/delivery/order/detail/{id} delivery GetDeliveryOrderDetail
//

//

//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: GetDeliveryOrderDetailReq
//
// Responses:
//  200: GetDeliveryOrderDetailResp

func GetDeliveryOrderDetailHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetDeliveryOrderDetailReq
		if err := httpx.Parse(r, &req, true); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := delivery.NewGetDeliveryOrderDetailLogic(r.Context(), svcCtx)
		resp, err := l.GetDeliveryOrderDetail(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
