package delivery

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"github.com/Wenpiner/iot-api/internal/logic/delivery"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
)

// swagger:route put /warehouse/delivery/order/confirm delivery ConfirmDeliveryOrder
//

//

//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: ConfirmDeliveryOrderReq
//
// Responses:
//  200: ConfirmDeliveryOrderResp

func ConfirmDeliveryOrderHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ConfirmDeliveryOrderReq
		if err := httpx.Parse(r, &req, true); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := delivery.NewConfirmDeliveryOrderLogic(r.Context(), svcCtx)
		resp, err := l.ConfirmDeliveryOrder(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
