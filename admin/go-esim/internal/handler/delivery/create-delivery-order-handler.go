package delivery

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"github.com/Wenpiner/iot-api/internal/logic/delivery"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
)

// swagger:route post /warehouse/delivery/order/create delivery CreateDeliveryOrder
//

//

//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: CreateDeliveryOrderReq
//
// Responses:
//  200: CreateDeliveryOrderResp

func CreateDeliveryOrderHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.CreateDeliveryOrderReq
		if err := httpx.Parse(r, &req, true); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := delivery.NewCreateDeliveryOrderLogic(r.Context(), svcCtx)
		resp, err := l.CreateDeliveryOrder(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
