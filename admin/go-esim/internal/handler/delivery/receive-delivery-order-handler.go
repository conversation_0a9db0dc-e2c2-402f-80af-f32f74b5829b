package delivery

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"github.com/Wenpiner/iot-api/internal/logic/delivery"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
)

// swagger:route put /warehouse/delivery/order/receive delivery ReceiveDeliveryOrder
//

//

//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: ReceiveDeliveryOrderReq
//
// Responses:
//  200: ReceiveDeliveryOrderResp

func ReceiveDeliveryOrderHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ReceiveDeliveryOrderReq
		if err := httpx.Parse(r, &req, true); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := delivery.NewReceiveDeliveryOrderLogic(r.Context(), svcCtx)
		resp, err := l.ReceiveDeliveryOrder(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
