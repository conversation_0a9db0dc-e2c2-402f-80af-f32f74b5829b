package inventory

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"github.com/Wenpiner/iot-api/internal/logic/inventory"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
)

// swagger:route post /warehouse/inventory/check inventory InventoryCheck
//

//

//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: InventoryCheckReq
//
// Responses:
//  200: InventoryCheckResp

func InventoryCheckHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.InventoryCheckReq
		if err := httpx.Parse(r, &req, true); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := inventory.NewInventoryCheckLogic(r.Context(), svcCtx)
		resp, err := l.InventoryCheck(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
