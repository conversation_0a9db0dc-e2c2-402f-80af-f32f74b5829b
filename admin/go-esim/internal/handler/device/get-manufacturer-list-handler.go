package device

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"github.com/Wenpiner/iot-api/internal/logic/device"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
)

// swagger:route post /device/manufacturer/list device GetManufacturerList
//
// 获取设备厂商列表
//
// 获取设备厂商列表
//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: ManufacturerListReq
//
// Responses:
//  200: ManufacturerListResp

func GetManufacturerListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ManufacturerListReq
		if err := httpx.Parse(r, &req, true); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := device.NewGetManufacturerListLogic(r.Context(), svcCtx)
		resp, err := l.GetManufacturerList(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
