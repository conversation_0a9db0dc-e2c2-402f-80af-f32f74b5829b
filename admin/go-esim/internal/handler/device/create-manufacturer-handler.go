package device

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"github.com/Wenpiner/iot-api/internal/logic/device"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
)

// swagger:route post /device/manufacturer/create device CreateManufacturer
//
// 创建/修改设备厂商
//
// 创建/修改设备厂商
//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: ManufacturerCreateReq
//
// Responses:
//  200: ManufacturerCreateResp

func CreateManufacturerHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ManufacturerCreateReq
		if err := httpx.Parse(r, &req, true); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := device.NewCreateManufacturerLogic(r.Context(), svcCtx)
		resp, err := l.CreateManufacturer(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
