package repo

import (
	"context"
	"fmt"

	"github.com/Wenpiner/iot-api/internal/model"
	"github.com/jackc/pgx/v5"
)

type DeviceManufacturerRepo interface {
	Create(ctx context.Context, manufacturer *model.DeviceManufacturer) error
	Update(ctx context.Context, manufacturer *model.DeviceManufacturer) error
	GetByID(ctx context.Context, id int64) (*model.DeviceManufacturer, error)
	Delete(ctx context.Context, id int64) error
}

type deviceManufacturerRepo struct {
	db *pgx.Conn
}

func NewDeviceManufacturerRepo(db *pgx.Conn) DeviceManufacturerRepo {
	return &deviceManufacturerRepo{db: db}
}

func (r *deviceManufacturerRepo) Create(ctx context.Context, manufacturer *model.DeviceManufacturer) error {
	query := `
		INSERT INTO iot.device_manufacturer (
			name, contact_person, contact_phone, contact_email,
			address, bank_account, bank_name
		) VALUES (
			@name, @contact_person, @contact_phone, @contact_email,
			@address, @bank_account, @bank_name
		) RETURNING id, created_at, updated_at
	`

	args := pgx.NamedArgs{
		"name":           manufacturer.Name,
		"contact_person": manufacturer.ContactPerson,
		"contact_phone":  manufacturer.ContactPhone,
		"contact_email":  manufacturer.ContactEmail,
		"address":        manufacturer.Address,
		"bank_account":   manufacturer.BankAccount,
		"bank_name":      manufacturer.BankName,
	}

	err := r.db.QueryRow(ctx, query, args).Scan(
		&manufacturer.ID,
		&manufacturer.CreatedAt,
		&manufacturer.UpdatedAt,
	)
	if err != nil {
		return fmt.Errorf("create device manufacturer: %w", err)
	}

	return nil
}

func (r *deviceManufacturerRepo) Update(ctx context.Context, manufacturer *model.DeviceManufacturer) error {
	query := `
		UPDATE iot.device_manufacturer SET
			name = @name,
			contact_person = @contact_person,
			contact_phone = @contact_phone,
			contact_email = @contact_email,
			address = @address,
			bank_account = @bank_account,
			bank_name = @bank_name,
			updated_at = CURRENT_TIMESTAMP
		WHERE id = @id
		RETURNING updated_at
	`

	args := pgx.NamedArgs{
		"id":             manufacturer.ID,
		"name":           manufacturer.Name,
		"contact_person": manufacturer.ContactPerson,
		"contact_phone":  manufacturer.ContactPhone,
		"contact_email":  manufacturer.ContactEmail,
		"address":        manufacturer.Address,
		"bank_account":   manufacturer.BankAccount,
		"bank_name":      manufacturer.BankName,
	}

	err := r.db.QueryRow(ctx, query, args).Scan(&manufacturer.UpdatedAt)
	if err != nil {
		return fmt.Errorf("update device manufacturer: %w", err)
	}

	return nil
}

func (r *deviceManufacturerRepo) GetByID(ctx context.Context, id int64) (*model.DeviceManufacturer, error) {
	query := `
		SELECT id, name, contact_person, contact_phone, contact_email,
			   address, bank_account, bank_name, created_at, updated_at
		FROM iot.device_manufacturer
		WHERE id = @id
	`

	args := pgx.NamedArgs{
		"id": id,
	}

	var manufacturer model.DeviceManufacturer
	err := r.db.QueryRow(ctx, query, args).Scan(
		&manufacturer.ID,
		&manufacturer.Name,
		&manufacturer.ContactPerson,
		&manufacturer.ContactPhone,
		&manufacturer.ContactEmail,
		&manufacturer.Address,
		&manufacturer.BankAccount,
		&manufacturer.BankName,
		&manufacturer.CreatedAt,
		&manufacturer.UpdatedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("get device manufacturer by id: %w", err)
	}

	return &manufacturer, nil
}

func (r *deviceManufacturerRepo) Delete(ctx context.Context, id int64) error {
	query := `
		DELETE FROM iot.device_manufacturer
		WHERE id = @id
	`

	args := pgx.NamedArgs{
		"id": id,
	}

	_, err := r.db.Exec(ctx, query, args)
	if err != nil {
		return fmt.Errorf("delete device manufacturer: %w", err)
	}

	return nil
}
