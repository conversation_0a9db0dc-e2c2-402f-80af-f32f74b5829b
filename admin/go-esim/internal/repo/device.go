package repo

import (
	"context"
	"errors"
	"fmt"

	"github.com/Wenpiner/iot-api/internal/types"
	"github.com/Wenpiner/iot-api/pkg/db"
	"github.com/georgysavva/scany/v2/pgxscan"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/shopspring/decimal"
	"github.com/zeromicro/go-zero/core/logx"
)

type DeviceRepo interface {
	// 新增导入设备
	CreateImportDevice(ctx context.Context, channelId int64, data *types.DeviceImportData, tags []string, cost decimal.Decimal) (int64, error)
}

type deviceRepo struct {
	db *pgxpool.Pool
}

func NewDeviceRepo(db *pgxpool.Pool) DeviceRepo {
	return &deviceRepo{db: db}
}

func (r *deviceRepo) CreateImportDevice(ctx context.Context, channelId int64, data *types.DeviceImportData, tags []string, cost decimal.Decimal) (int64, error) {
	// 开启事务
	tx, err := r.db.Begin(ctx)
	if err != nil {
		return 0, err
	}
	defer tx.Rollback(ctx)

	// 验证设备是否已被入库
	sql := `SELECT EXISTS(SELECT 1 FROM iot.devices WHERE device_no = $1)`

	var exists bool
	err = tx.QueryRow(ctx, sql, data.DeviceNumber).Scan(&exists)
	if err != nil {
		return 0, errors.New("查询设备是否存在失败")
	}

	if exists {
		return 0, errors.New("设备已被入库")
	}

	// 查询对应的卡是否已被绑定
	simIds := []string{data.ESIM2ID, data.ESIM3ID, data.ESIMID}
	// 去掉空值
	for i := 0; i < len(simIds); i++ {
		if simIds[i] == "" {
			simIds = append(simIds[:i], simIds[i+1:]...)
			i--
		}
	}

	// 查询卡是否已被绑定
	sql = `SELECT iccid FROM iot.cards WHERE iccid = ANY($1) and device_id is not null`
	var iccids []string
	err = pgxscan.Select(ctx, tx, &iccids, sql, simIds)
	if err != nil {
		logx.Errorf("查询卡是否已被绑定失败: %v", err)
		return 0, errors.New("查询卡是否已被绑定失败")
	}
	if len(iccids) > 0 {
		return 0, fmt.Errorf("卡 {%s} 已被绑定其他设备", iccids)
	}

	// 开始插入设备信息
	params := make(pgx.NamedArgs)
	params["box_no"] = data.BoxNumber
	params["random_code"] = data.RandomCode
	params["package_date"] = data.PackageTime
	params["device_no"] = data.DeviceNumber
	params["imei"] = data.IMEI
	params["imsi"] = data.IMSI
	params["mac"] = data.MAC
	params["msisdn"] = data.MSISDN
	params["ccid"] = data.CCID
	params["access_number"] = data.AccessNumber
	params["sn"] = data.SN
	params["ssid"] = data.SSID
	params["wifi_key"] = data.WIFIKEY
	params["wifi_mac"] = data.WIFIMAC
	params["wifi_mac_5g"] = data.WIFIMAC5G
	params["tags"] = tags
	params["cost_price"] = cost
	params["channel_id"] = channelId

	row := db.InsertWithReturning(ctx, "iot.devices", params, tx.QueryRow, "id")
	var id int64
	err = row.Scan(&id)
	if err != nil {
		logx.Errorf("写入设备信息失败: %v", err)
		return 0, errors.New("写入设备信息失败")
	}

	// 对卡进行设备绑定
	for _, simId := range simIds {
		sql = `UPDATE iot.cards SET device_id = $1,updated_at = CURRENT_TIMESTAMP WHERE iccid = $2 RETURNING id`
		var cardId int64
		err = tx.QueryRow(ctx, sql, id, simId).Scan(&cardId)
		if err != nil {
			if errors.Is(err, pgx.ErrNoRows) {
				return 0, fmt.Errorf("卡 {%s} 在系统中不存在", simId)
			}
			logx.Errorf("绑定卡失败: %v", err)
			return 0, errors.New("绑定卡失败")
		}
		bindingsParams := make(pgx.NamedArgs)
		bindingsParams["device_id"] = id
		bindingsParams["card_id"] = cardId
		_ = db.Insert(ctx, "iot.device_card_bindings", bindingsParams, tx.Exec)
	}

	// 提交事务
	err = tx.Commit(ctx)
	if err != nil {
		return 0, err
	}

	return id, nil
}
