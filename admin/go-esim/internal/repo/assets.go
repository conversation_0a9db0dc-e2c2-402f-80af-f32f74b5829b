package repo

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"github.com/Wenpiner/iot-api/internal/model"
	"github.com/Wenpiner/iot-api/pkg/lock"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/redis/go-redis/v9"
)

// 资产锁获取失败错误
var ErrWriteLock = errors.New("资产锁获取失败")

// 资产锁已被占用错误
var ErrWriteLockOccupied = errors.New("资产锁已被占用")

// 资产
type AssetsRepo interface {
	// 创建资产
	Create(ctx context.Context, userId uint32, assetType model.AssetType) (assets *model.Assets, err error)

	// 获取资产信息
	Get(ctx context.Context, userId uint32, assetType model.AssetType, isUpdate bool) (assets *model.Assets, err error)

	// 获取资产写入锁，并返回一个锁函数
	GetWriteLock(ctx context.Context, userId uint32, assetType model.AssetType) (l lock.FairLock, err error)
}

type assetsRepo struct {
	db *pgxpool.Pool
	rd redis.UniversalClient
}

// NewAssetsRepo 创建资产仓库
func NewAssetsRepo(db *pgxpool.Pool, rd redis.UniversalClient) AssetsRepo {
	return &assetsRepo{db: db, rd: rd}
}

// getDB 获取资产信息，通过数据库查询
func (r *assetsRepo) getDB(ctx context.Context, userId uint32, assetType model.AssetType) (assets *model.Assets, err error) {
	sql := `
		SELECT id, user_id, balance, created_at, updated_at, lock_withdrawal, asset_type FROM iot.assets WHERE user_id = $1 AND asset_type = $2
	`
	assets = &model.Assets{}
	row := r.db.QueryRow(ctx, sql, userId, assetType)
	err = row.Scan(&assets.ID, &assets.UserID, &assets.Balance, &assets.CreatedAt, &assets.UpdatedAt, &assets.LockWithdrawal, &assets.AssetType)
	if err != nil {
		return nil, err
	}
	return assets, nil
}

// Create 创建资产
func (r *assetsRepo) Create(ctx context.Context, userId uint32, assetType model.AssetType) (assets *model.Assets, err error) {
	sql := `
		INSERT INTO assets (user_id, asset_type)
		VALUES ($1, $2)
		RETURNING user_id, balance, created_at, updated_at, lock_withdrawal, asset_type
	`
	assets = &model.Assets{}
	row := r.db.QueryRow(ctx, sql, userId, assetType)
	err = row.Scan(&assets.UserID, &assets.Balance, &assets.CreatedAt, &assets.UpdatedAt, &assets.LockWithdrawal, &assets.AssetType)
	if err != nil {
		return nil, err
	}

	return assets, nil
}

// Get 获取资产信息，通过将ID缓存到redis中,如果redis不存在就查询全量数据字段，如果存在则通过ID查询updated_at、balance、lock_withdrawal
func (r *assetsRepo) Get(ctx context.Context, userId uint32, assetType model.AssetType, isUpdate bool) (assets *model.Assets, err error) {
	redisKey := fmt.Sprintf("assets:%d:%s", userId, assetType)
	value, err := r.rd.Get(ctx, redisKey).Result()
	if err != nil {
		// 查询全量数据字段
		assets, err = r.getDB(ctx, userId, assetType)
		if err != nil {
			return nil, err
		}
		// 将数据缓存到redis中
		r.rd.Set(ctx, redisKey, assets, 0)
		return assets, nil
	} else {
		assets = &model.Assets{}
		// 解析json
		err = json.Unmarshal([]byte(value), &assets)
		if err != nil {
			return nil, err
		}
		if isUpdate {
			// 通过ID查询updated_at、balance、lock_withdrawal
			sql := `
		SELECT updated_at, balance, lock_withdrawal FROM iot.assets WHERE id = $1	
	`
			row := r.db.QueryRow(ctx, sql, assets.ID)
			err = row.Scan(&assets.UpdatedAt, &assets.Balance, &assets.LockWithdrawal)
			if err != nil {
				return nil, err
			}
			// 将数据缓存到redis中
			r.rd.Set(ctx, redisKey, assets, 0)
		}
	}

	return assets, nil
}

// GetWriteLock 获取资产写入锁，并返回一个锁函数
func (r *assetsRepo) GetWriteLock(ctx context.Context, userId uint32, assetType model.AssetType) (l lock.FairLock, err error) {
	// 查询资产信息
	assets, err := r.Get(ctx, userId, assetType, false)
	if err != nil {
		return nil, err
	}
	//
	l, err = lock.NewFairLock(fmt.Sprintf("lock:assets:%d", assets.ID), lock.NewRedisBackend(r.rd))
	if err != nil {
		return nil, err
	}
	return l, nil
}
