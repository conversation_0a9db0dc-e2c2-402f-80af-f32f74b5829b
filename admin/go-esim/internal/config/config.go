package config

import (
	"github.com/suyuan32/simple-admin-common/config"
	"github.com/suyuan32/simple-admin-common/plugins/casbin"
	"github.com/wenpiner/rabbitmq-go/conf"
	"github.com/zeromicro/go-zero/rest"
)

type Config struct {
	rest.RestConf
	Auth         rest.AuthConf
	CROSConf     config.CROSConf
	DatabaseConf config.DatabaseConf
	RedisConf    config.RedisConf
	CasbinConf   casbin.CasbinConf
	RabbitMQConf conf.RabbitConf
	QimenConf    QimenConf
}

// QimenConf 奇门API配置
type QimenConf struct {
	// 默认超时时间（秒）
	DefaultTimeout int `json:",default=30"`
	// 最大重试次数
	MaxRetryCount int `json:",default=3"`
	// 重试间隔（秒）
	RetryInterval int `json:",default=5"`
}
