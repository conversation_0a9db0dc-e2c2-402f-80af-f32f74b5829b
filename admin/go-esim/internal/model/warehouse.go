package model

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/Wenpiner/iot-api/pkg/constants"
	"github.com/Wenpiner/iot-api/pkg/errors"
	"github.com/Wenpiner/iot-api/pkg/utils"
	"github.com/georgysavva/scany/v2/pgxscan"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/zeromicro/go-zero/core/logx"
)

// Warehouse 云仓模型
type Warehouse struct {
	ID              int64     `db:"id" json:"id"`
	WarehouseCode   string    `db:"warehouse_code" json:"warehouse_code"`
	WarehouseName   string    `db:"warehouse_name" json:"warehouse_name"`
	CompanyName     string    `db:"company_name" json:"company_name"`
	ContactPerson   string    `db:"contact_person" json:"contact_person"`
	ContactPhone    string    `db:"contact_phone" json:"contact_phone"`
	ContactEmail    string    `db:"contact_email" json:"contact_email"`
	Address         string    `db:"address" json:"address"`
	Province        string    `db:"province" json:"province"`
	City            string    `db:"city" json:"city"`
	District        string    `db:"district" json:"district"`
	ApiEndpoint     string    `db:"api_endpoint" json:"api_endpoint"`
	AppKey          string    `db:"app_key" json:"app_key"`
	AppSecret       string    `db:"app_secret" json:"app_secret"`
	CustomerID      string    `db:"customer_id" json:"customer_id"`
	BankName        string    `db:"bank_name" json:"bank_name"`
	BankAccount     string    `db:"bank_account" json:"bank_account"`
	BankAccountName string    `db:"bank_account_name" json:"bank_account_name"`
	Status          int32     `db:"status" json:"status"`
	CreatedAt       time.Time `db:"created_at" json:"created_at"`
	UpdatedAt       time.Time `db:"updated_at" json:"updated_at"`
}

// WarehouseRepo 云仓仓储
type WarehouseRepo struct {
	db     *pgxpool.Pool
	logger logx.Logger
}

// NewWarehouseRepo 创建云仓仓储
func NewWarehouseRepo(db *pgxpool.Pool) *WarehouseRepo {
	return &WarehouseRepo{
		db:     db,
		logger: logx.WithContext(context.Background()),
	}
}

// Create 创建云仓
func (r *WarehouseRepo) Create(ctx context.Context, warehouse *Warehouse) error {
	// 验证参数
	if err := r.validateWarehouse(warehouse); err != nil {
		return err
	}

	// 检查编码是否已存在
	exists, err := r.ExistsByCode(ctx, warehouse.WarehouseCode)
	if err != nil {
		return err
	}
	if exists {
		return errors.ErrInvalidParam.WithDetail("云仓编码已存在")
	}

	sql := `
		INSERT INTO iot.warehouses (
			warehouse_code, warehouse_name, company_name, contact_person, 
			contact_phone, contact_email, address, province, city, district,
			api_endpoint, app_key, app_secret, customer_id, bank_name,
			bank_account, bank_account_name, status, created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10,
			$11, $12, $13, $14, $15, $16, $17, $18, NOW(), NOW()
		) RETURNING id, created_at, updated_at`

	err = r.db.QueryRow(ctx, sql,
		warehouse.WarehouseCode, warehouse.WarehouseName, warehouse.CompanyName,
		warehouse.ContactPerson, warehouse.ContactPhone, warehouse.ContactEmail,
		warehouse.Address, warehouse.Province, warehouse.City, warehouse.District,
		warehouse.ApiEndpoint, warehouse.AppKey, warehouse.AppSecret,
		warehouse.CustomerID, warehouse.BankName, warehouse.BankAccount,
		warehouse.BankAccountName, warehouse.Status,
	).Scan(&warehouse.ID, &warehouse.CreatedAt, &warehouse.UpdatedAt)

	if err != nil {
		r.logger.Errorf("创建云仓失败: %v", err)
		return errors.ErrDatabaseError.WithDetail(err.Error())
	}

	r.logger.Infof("创建云仓成功: ID=%d, Code=%s", warehouse.ID, warehouse.WarehouseCode)
	return nil
}

// Update 更新云仓
func (r *WarehouseRepo) Update(ctx context.Context, warehouse *Warehouse) error {
	// 验证参数
	if warehouse.ID <= 0 {
		return errors.ErrInvalidParam.WithDetail("云仓ID无效")
	}

	// 构建动态更新SQL
	setParts := []string{}
	args := []interface{}{}
	argIndex := 1

	if warehouse.WarehouseName != "" {
		setParts = append(setParts, fmt.Sprintf("warehouse_name = $%d", argIndex))
		args = append(args, warehouse.WarehouseName)
		argIndex++
	}
	if warehouse.CompanyName != "" {
		setParts = append(setParts, fmt.Sprintf("company_name = $%d", argIndex))
		args = append(args, warehouse.CompanyName)
		argIndex++
	}
	if warehouse.ContactPerson != "" {
		setParts = append(setParts, fmt.Sprintf("contact_person = $%d", argIndex))
		args = append(args, warehouse.ContactPerson)
		argIndex++
	}
	if warehouse.ContactPhone != "" {
		setParts = append(setParts, fmt.Sprintf("contact_phone = $%d", argIndex))
		args = append(args, warehouse.ContactPhone)
		argIndex++
	}
	if warehouse.ContactEmail != "" {
		setParts = append(setParts, fmt.Sprintf("contact_email = $%d", argIndex))
		args = append(args, warehouse.ContactEmail)
		argIndex++
	}
	if warehouse.Address != "" {
		setParts = append(setParts, fmt.Sprintf("address = $%d", argIndex))
		args = append(args, warehouse.Address)
		argIndex++
	}
	if warehouse.Province != "" {
		setParts = append(setParts, fmt.Sprintf("province = $%d", argIndex))
		args = append(args, warehouse.Province)
		argIndex++
	}
	if warehouse.City != "" {
		setParts = append(setParts, fmt.Sprintf("city = $%d", argIndex))
		args = append(args, warehouse.City)
		argIndex++
	}
	if warehouse.District != "" {
		setParts = append(setParts, fmt.Sprintf("district = $%d", argIndex))
		args = append(args, warehouse.District)
		argIndex++
	}
	if warehouse.ApiEndpoint != "" {
		setParts = append(setParts, fmt.Sprintf("api_endpoint = $%d", argIndex))
		args = append(args, warehouse.ApiEndpoint)
		argIndex++
	}
	if warehouse.AppKey != "" {
		setParts = append(setParts, fmt.Sprintf("app_key = $%d", argIndex))
		args = append(args, warehouse.AppKey)
		argIndex++
	}
	if warehouse.AppSecret != "" {
		setParts = append(setParts, fmt.Sprintf("app_secret = $%d", argIndex))
		args = append(args, warehouse.AppSecret)
		argIndex++
	}
	if warehouse.CustomerID != "" {
		setParts = append(setParts, fmt.Sprintf("customer_id = $%d", argIndex))
		args = append(args, warehouse.CustomerID)
		argIndex++
	}
	if warehouse.Status > 0 {
		setParts = append(setParts, fmt.Sprintf("status = $%d", argIndex))
		args = append(args, warehouse.Status)
		argIndex++
	}

	if len(setParts) == 0 {
		return errors.ErrInvalidParam.WithDetail("没有需要更新的字段")
	}

	// 添加更新时间和ID条件
	setParts = append(setParts, fmt.Sprintf("updated_at = NOW()"))
	args = append(args, warehouse.ID)

	sql := fmt.Sprintf(`
		UPDATE iot.warehouses 
		SET %s 
		WHERE id = $%d
		RETURNING updated_at`, strings.Join(setParts, ", "), argIndex)

	err := r.db.QueryRow(ctx, sql, args...).Scan(&warehouse.UpdatedAt)
	if err != nil {
		if err == pgx.ErrNoRows {
			return errors.ErrWarehouseNotFound
		}
		r.logger.Errorf("更新云仓失败: %v", err)
		return errors.ErrDatabaseError.WithDetail(err.Error())
	}

	r.logger.Infof("更新云仓成功: ID=%d", warehouse.ID)
	return nil
}

// GetByID 根据ID获取云仓
func (r *WarehouseRepo) GetByID(ctx context.Context, id int64) (*Warehouse, error) {
	if id <= 0 {
		return nil, errors.ErrInvalidParam.WithDetail("云仓ID无效")
	}

	sql := `
		SELECT id, warehouse_code, warehouse_name, company_name, contact_person,
			   contact_phone, contact_email, address, province, city, district,
			   api_endpoint, app_key, app_secret, customer_id, bank_name,
			   bank_account, bank_account_name, status, created_at, updated_at
		FROM iot.warehouses 
		WHERE id = $1`

	var warehouse Warehouse
	err := pgxscan.Get(ctx, r.db, &warehouse, sql, id)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.ErrWarehouseNotFound
		}
		r.logger.Errorf("查询云仓失败: %v", err)
		return nil, errors.ErrDatabaseError.WithDetail(err.Error())
	}

	return &warehouse, nil
}

// GetByCode 根据编码获取云仓
func (r *WarehouseRepo) GetByCode(ctx context.Context, code string) (*Warehouse, error) {
	if code == "" {
		return nil, errors.ErrInvalidParam.WithDetail("云仓编码不能为空")
	}

	sql := `
		SELECT id, warehouse_code, warehouse_name, company_name, contact_person,
			   contact_phone, contact_email, address, province, city, district,
			   api_endpoint, app_key, app_secret, customer_id, bank_name,
			   bank_account, bank_account_name, status, created_at, updated_at
		FROM iot.warehouses 
		WHERE warehouse_code = $1`

	var warehouse Warehouse
	err := pgxscan.Get(ctx, r.db, &warehouse, sql, code)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.ErrWarehouseNotFound
		}
		r.logger.Errorf("查询云仓失败: %v", err)
		return nil, errors.ErrDatabaseError.WithDetail(err.Error())
	}

	return &warehouse, nil
}

// ExistsByCode 检查编码是否存在
func (r *WarehouseRepo) ExistsByCode(ctx context.Context, code string) (bool, error) {
	if code == "" {
		return false, errors.ErrInvalidParam.WithDetail("云仓编码不能为空")
	}

	sql := `SELECT EXISTS(SELECT 1 FROM iot.warehouses WHERE warehouse_code = $1)`

	var exists bool
	err := r.db.QueryRow(ctx, sql, code).Scan(&exists)
	if err != nil {
		r.logger.Errorf("检查云仓编码是否存在失败: %v", err)
		return false, errors.ErrDatabaseError.WithDetail(err.Error())
	}

	return exists, nil
}

// validateWarehouse 验证云仓数据
func (r *WarehouseRepo) validateWarehouse(warehouse *Warehouse) error {
	if warehouse == nil {
		return errors.ErrInvalidParam.WithDetail("云仓信息不能为空")
	}
	if !utils.ValidateWarehouseCode(warehouse.WarehouseCode) {
		return errors.ErrInvalidParam.WithDetail("云仓编码格式无效")
	}
	if warehouse.WarehouseName == "" {
		return errors.ErrInvalidParam.WithDetail("云仓名称不能为空")
	}
	if warehouse.CompanyName == "" {
		return errors.ErrInvalidParam.WithDetail("公司名称不能为空")
	}
	if warehouse.ContactPerson == "" {
		return errors.ErrInvalidParam.WithDetail("联系人不能为空")
	}
	if !utils.ValidatePhone(warehouse.ContactPhone) {
		return errors.ErrInvalidParam.WithDetail("联系电话格式无效")
	}
	if !utils.ValidateEmail(warehouse.ContactEmail) {
		return errors.ErrInvalidParam.WithDetail("邮箱格式无效")
	}
	if warehouse.Address == "" {
		return errors.ErrInvalidParam.WithDetail("地址不能为空")
	}
	if warehouse.Province == "" {
		return errors.ErrInvalidParam.WithDetail("省份不能为空")
	}
	if warehouse.City == "" {
		return errors.ErrInvalidParam.WithDetail("城市不能为空")
	}
	if warehouse.ApiEndpoint == "" {
		return errors.ErrInvalidParam.WithDetail("API接口地址不能为空")
	}
	if warehouse.AppKey == "" {
		return errors.ErrInvalidParam.WithDetail("AppKey不能为空")
	}
	if warehouse.AppSecret == "" {
		return errors.ErrInvalidParam.WithDetail("AppSecret不能为空")
	}
	if warehouse.CustomerID == "" {
		return errors.ErrInvalidParam.WithDetail("客户ID不能为空")
	}
	if warehouse.Status != constants.WarehouseStatusNormal && warehouse.Status != constants.WarehouseStatusDisabled {
		return errors.ErrInvalidParam.WithDetail("云仓状态无效")
	}

	return nil
}

// WarehouseListQuery 云仓列表查询参数
type WarehouseListQuery struct {
	Page     int64  `json:"page"`
	PageSize int64  `json:"page_size"`
	Keyword  string `json:"keyword"`
	Status   int32  `json:"status"`
}

// WarehouseListResult 云仓列表查询结果
type WarehouseListResult struct {
	List  []*Warehouse `json:"list"`
	Total int64        `json:"total"`
}

// GetList 获取云仓列表
func (r *WarehouseRepo) GetList(ctx context.Context, query *WarehouseListQuery) (*WarehouseListResult, error) {
	// 验证分页参数
	if query.Page <= 0 {
		query.Page = 1
	}
	if query.PageSize <= 0 || query.PageSize > 100 {
		query.PageSize = 20
	}

	// 构建查询条件
	whereParts := []string{}
	args := []interface{}{}
	argIndex := 1

	// 关键词搜索
	if query.Keyword != "" {
		whereParts = append(whereParts, fmt.Sprintf("(warehouse_name ILIKE $%d OR warehouse_code ILIKE $%d)", argIndex, argIndex))
		args = append(args, "%"+query.Keyword+"%")
		argIndex++
	}

	// 状态筛选
	if query.Status > 0 {
		whereParts = append(whereParts, fmt.Sprintf("status = $%d", argIndex))
		args = append(args, query.Status)
		argIndex++
	}

	whereClause := ""
	if len(whereParts) > 0 {
		whereClause = "WHERE " + strings.Join(whereParts, " AND ")
	}

	// 查询总数
	countSQL := fmt.Sprintf("SELECT COUNT(*) FROM iot.warehouses %s", whereClause)
	var total int64
	err := r.db.QueryRow(ctx, countSQL, args...).Scan(&total)
	if err != nil {
		r.logger.Errorf("查询云仓总数失败: %v", err)
		return nil, errors.ErrDatabaseError.WithDetail(err.Error())
	}

	// 查询列表
	offset := (query.Page - 1) * query.PageSize
	listSQL := fmt.Sprintf(`
		SELECT id, warehouse_code, warehouse_name, company_name, contact_person,
			   contact_phone, contact_email, address, province, city, district,
			   api_endpoint, app_key, app_secret, customer_id, bank_name,
			   bank_account, bank_account_name, status, created_at, updated_at
		FROM iot.warehouses
		%s
		ORDER BY created_at DESC
		LIMIT $%d OFFSET $%d`, whereClause, argIndex, argIndex+1)

	args = append(args, query.PageSize, offset)

	var warehouses []*Warehouse
	err = pgxscan.Select(ctx, r.db, &warehouses, listSQL, args...)
	if err != nil {
		r.logger.Errorf("查询云仓列表失败: %v", err)
		return nil, errors.ErrDatabaseError.WithDetail(err.Error())
	}

	return &WarehouseListResult{
		List:  warehouses,
		Total: total,
	}, nil
}

// GetActiveWarehouses 获取所有启用的云仓
func (r *WarehouseRepo) GetActiveWarehouses(ctx context.Context) ([]*Warehouse, error) {
	sql := `
		SELECT id, warehouse_code, warehouse_name, company_name, contact_person,
			   contact_phone, contact_email, address, province, city, district,
			   api_endpoint, app_key, app_secret, customer_id, bank_name,
			   bank_account, bank_account_name, status, created_at, updated_at
		FROM iot.warehouses
		WHERE status = $1
		ORDER BY warehouse_name`

	var warehouses []*Warehouse
	err := pgxscan.Select(ctx, r.db, &warehouses, sql, constants.WarehouseStatusNormal)
	if err != nil {
		r.logger.Errorf("查询启用云仓失败: %v", err)
		return nil, errors.ErrDatabaseError.WithDetail(err.Error())
	}

	return warehouses, nil
}
