package model

import (
	"context"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

// DeviceManufacturer 设备厂商
type DeviceManufacturer struct {
	ID            int64     `db:"id"`
	Name          string    `db:"name"`
	Contact<PERSON>erson string    `db:"contact_person"`
	ContactPhone  string    `db:"contact_phone"`
	ContactEmail  string    `db:"contact_email"`
	Address       string    `db:"address"`
	BankAccount   string    `db:"bank_account"`
	BankName      string    `db:"bank_name"`
	CreatedAt     time.Time `db:"created_at"`
	UpdatedAt     time.Time `db:"updated_at"`
}

type DeviceManufacturerRepo struct {
	db *pgxpool.Pool
}

func NewDeviceManufacturerRepo(db *pgxpool.Pool) *DeviceManufacturerRepo {
	return &DeviceManufacturerRepo{db: db}
}

func (r *DeviceManufacturerRepo) Create(ctx context.Context, manufacturer *DeviceManufacturer) error {
	query := `
		INSERT INTO iot.device_manufacturer (
			name, contact_person, contact_phone, contact_email,
			address, bank_account, bank_name
		) VALUES (
			@name, @contact_person, @contact_phone, @contact_email,
			@address, @bank_account, @bank_name
		) RETURNING id, created_at, updated_at
	`

	args := pgx.NamedArgs{
		"name":           manufacturer.Name,
		"contact_person": manufacturer.ContactPerson,
		"contact_phone":  manufacturer.ContactPhone,
		"contact_email":  manufacturer.ContactEmail,
		"address":        manufacturer.Address,
		"bank_account":   manufacturer.BankAccount,
		"bank_name":      manufacturer.BankName,
	}

	err := r.db.QueryRow(ctx, query, args).Scan(
		&manufacturer.ID,
		&manufacturer.CreatedAt,
		&manufacturer.UpdatedAt,
	)
	if err != nil {
		return fmt.Errorf("create device manufacturer: %w", err)
	}

	return nil
}

func (r *DeviceManufacturerRepo) Update(ctx context.Context, manufacturer *DeviceManufacturer) error {
	query := `
		UPDATE iot.device_manufacturer SET
			name = @name,
			contact_person = @contact_person,
			contact_phone = @contact_phone,
			contact_email = @contact_email,
			address = @address,
			bank_account = @bank_account,
			bank_name = @bank_name,
			updated_at = CURRENT_TIMESTAMP
		WHERE id = @id
		RETURNING updated_at
	`

	args := pgx.NamedArgs{
		"id":             manufacturer.ID,
		"name":           manufacturer.Name,
		"contact_person": manufacturer.ContactPerson,
		"contact_phone":  manufacturer.ContactPhone,
		"contact_email":  manufacturer.ContactEmail,
		"address":        manufacturer.Address,
		"bank_account":   manufacturer.BankAccount,
		"bank_name":      manufacturer.BankName,
	}

	err := r.db.QueryRow(ctx, query, args).Scan(&manufacturer.UpdatedAt)
	if err != nil {
		return fmt.Errorf("update device manufacturer: %w", err)
	}

	return nil
}

func (r *DeviceManufacturerRepo) GetByID(ctx context.Context, id int64) (*DeviceManufacturer, error) {
	query := `
		SELECT id, name, contact_person, contact_phone, contact_email,
			   address, bank_account, bank_name, created_at, updated_at
		FROM iot.device_manufacturer
		WHERE id = @id
	`

	args := pgx.NamedArgs{
		"id": id,
	}

	var manufacturer DeviceManufacturer
	err := r.db.QueryRow(ctx, query, args).Scan(
		&manufacturer.ID,
		&manufacturer.Name,
		&manufacturer.ContactPerson,
		&manufacturer.ContactPhone,
		&manufacturer.ContactEmail,
		&manufacturer.Address,
		&manufacturer.BankAccount,
		&manufacturer.BankName,
		&manufacturer.CreatedAt,
		&manufacturer.UpdatedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("get device manufacturer by id: %w", err)
	}

	return &manufacturer, nil
}

func (r *DeviceManufacturerRepo) Delete(ctx context.Context, id int64) error {
	query := `
		DELETE FROM iot.device_manufacturer
		WHERE id = @id
	`

	args := pgx.NamedArgs{
		"id": id,
	}

	_, err := r.db.Exec(ctx, query, args)
	if err != nil {
		return fmt.Errorf("delete device manufacturer: %w", err)
	}

	return nil
}

// Save 保存设备厂商信息，如果 ID 为 0 则创建，否则更新
func (r *DeviceManufacturerRepo) Save(ctx context.Context, manufacturer *DeviceManufacturer) error {
	if manufacturer.ID == 0 {
		return r.Create(ctx, manufacturer)
	}
	return r.Update(ctx, manufacturer)
}
