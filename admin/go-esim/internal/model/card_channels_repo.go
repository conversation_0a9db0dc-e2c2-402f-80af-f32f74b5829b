package model

import (
	"context"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type CardChannelRepo struct {
	db *pgxpool.Pool
}

func NewCardChannelRepo(db *pgxpool.Pool) *CardChannelRepo {
	return &CardChannelRepo{db: db}
}

func (r *CardChannelRepo) Create(ctx context.Context, channel *CardChannel) error {
	var query string
	var extraData JSONB
	if channel.ExtraData == nil {
		extraData = make(JSONB)
	} else {
		extraData = channel.ExtraData
	}

	args := make(pgx.NamedArgs)
	args["name"] = channel.Name
	args["region"] = channel.Region
	args["contact_person"] = channel.ContactPerson
	args["contact_phone"] = channel.ContactPhone
	args["real_name_required"] = channel.RealNameRequired
	args["channel_type"] = channel.ChannelType
	args["extra_data"] = extraData
	args["real_name_url"] = channel.RealNameUrl
	args["operator"] = channel.Operator

	if channel.ID > 0 {
		// 更新操作
		query = `
			UPDATE iot.card_channels SET
				name = @name,
				region = @region,
				contact_person = @contact_person,
				contact_phone = @contact_phone,
				real_name_required = @real_name_required,
				channel_type = @channel_type,
				extra_data = @extra_data,
				real_name_url = @real_name_url,
				operator = @operator,
				updated_at = CURRENT_TIMESTAMP
			WHERE id = @id
			RETURNING id, created_at, updated_at
		`
		args["id"] = channel.ID
	} else {
		// 新增操作
		query = `
			INSERT INTO iot.card_channels (
				name, region, contact_person, contact_phone, real_name_required,
				channel_type, extra_data, real_name_url, balance, operator
			) VALUES (
				@name, @region, @contact_person, @contact_phone, @real_name_required,
				@channel_type, @extra_data, @real_name_url, @balance, @operator
			) RETURNING id, created_at, updated_at
		`
	}

	return r.db.QueryRow(ctx, query, args).Scan(&channel.ID, &channel.CreatedAt, &channel.UpdatedAt)
}
