package model

import (
	"context"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type TaskRepo struct {
	db *pgxpool.Pool
}

func NewTaskRepo(db *pgxpool.Pool) *TaskRepo {
	return &TaskRepo{db: db}
}

// 新增任务日志
func (r *TaskRepo) CreateTaskLog(ctx context.Context, taskID, status int, record_id int32, record_text, error_text string) error {
	query := `
		INSERT INTO iot.task_log (task_id, record_id,record_text,status,error_text)
		VALUES (@task_id, @record_id, @record_text, @status, @error_text)
	`
	_, err := r.db.Exec(ctx, query, pgx.NamedArgs{
		"task_id":     taskID,
		"status":      status,
		"record_id":   record_id,
		"record_text": record_text,
		"error_text":  error_text,
	})
	return err
}
