package model

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"
)

type CardChannel struct {
	ID               int64     `db:"id"`
	Name             string    `db:"name"`
	Region           string    `db:"region"`
	ContactPerson    string    `db:"contact_person"`
	ContactPhone     string    `db:"contact_phone"`
	RealNameRequired bool      `db:"real_name_required"`
	ChannelType      string    `db:"channel_type"`
	ExtraData        JSONB     `db:"extra_data"`
	CreatedAt        time.Time `db:"created_at"`
	UpdatedAt        time.Time `db:"updated_at"`
	RealNameUrl      string    `db:"real_name_url"`
	Balance          float64   `db:"balance"`
	Operator         string    `db:"operator"`
}

// JSONB 是一个自定义类型，用于处理 PostgreSQL 的 JSONB 类型
type JSONB map[string]interface{}

// Value 实现 driver.Valuer 接口
func (j JSONB) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// Scan 实现 sql.Scanner 接口
func (j *JSONB) Scan(value interface{}) error {
	if value == nil {
		*j = make(JSONB)
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return errors.New("cannot scan into JSONB")
	}

	if *j == nil {
		*j = make(JSONB)
	}

	return json.Unmarshal(bytes, j)
}

// 工具方法 - 获取字符串值
func (j JSONB) GetString(key string) string {
	if val, ok := j[key]; ok {
		if str, ok := val.(string); ok {
			return str
		}
	}
	return ""
}

// 工具方法 - 获取整数值
func (j JSONB) GetInt(key string) int {
	if val, ok := j[key]; ok {
		switch v := val.(type) {
		case int:
			return v
		case float64:
			return int(v)
		}
	}
	return 0
}

// 工具方法 - 获取布尔值
func (j JSONB) GetBool(key string) bool {
	if val, ok := j[key]; ok {
		if b, ok := val.(bool); ok {
			return b
		}
	}
	return false
}

// 工具方法 - 获取嵌套对象
func (j JSONB) GetMap(key string) map[string]interface{} {
	if val, ok := j[key]; ok {
		if m, ok := val.(map[string]interface{}); ok {
			return m
		}
	}
	return nil
}

// 工具方法 - 获取数组
func (j JSONB) GetArray(key string) []interface{} {
	if val, ok := j[key]; ok {
		if arr, ok := val.([]interface{}); ok {
			return arr
		}
	}
	return nil
}

// 工具方法 - 设置值
func (j JSONB) Set(key string, value interface{}) {
	j[key] = value
}

// 工具方法 - 删除键
func (j JSONB) Delete(key string) {
	delete(j, key)
}

// 工具方法 - 检查键是否存在
func (j JSONB) Has(key string) bool {
	_, ok := j[key]
	return ok
}

// 工具方法 - 深度设置嵌套值
func (j JSONB) SetNested(path []string, value interface{}) {
	if len(path) == 0 {
		return
	}

	current := map[string]interface{}(j)
	for _, key := range path[:len(path)-1] {
		if next, ok := current[key]; ok {
			if nextMap, ok := next.(map[string]interface{}); ok {
				current = nextMap
			} else {
				// 如果路径上的值不是 map，创建新的 map
				newMap := make(map[string]interface{})
				current[key] = newMap
				current = newMap
			}
		} else {
			// 创建新的嵌套 map
			newMap := make(map[string]interface{})
			current[key] = newMap
			current = newMap
		}
	}

	current[path[len(path)-1]] = value
}

// 实现类似json的String方法
func (j *JSONB) String() string {
	jsonBytes, err := json.Marshal(j)
	if err != nil {
		return ""
	}
	return string(jsonBytes)
}
