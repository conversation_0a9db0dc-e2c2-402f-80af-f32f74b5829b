package model

import (
	"context"
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/Wenpiner/iot-api/pkg/constants"
	"github.com/Wenpiner/iot-api/pkg/errors"
	"github.com/Wenpiner/iot-api/pkg/utils"
	"github.com/georgysavva/scany/v2/pgxscan"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/zeromicro/go-zero/core/logx"
)

// EntryOrder 入库单模型
type EntryOrder struct {
	ID                int64      `db:"id" json:"id"`
	EntryOrderCode    string     `db:"entry_order_code" json:"entry_order_code"`
	WarehouseCode     string     `db:"warehouse_code" json:"warehouse_code"`
	WarehouseName     string     `db:"warehouse_name" json:"warehouse_name"`
	OwnerCode         string     `db:"owner_code" json:"owner_code"`
	OrderType         string     `db:"order_type" json:"order_type"`
	OrderCreateTime   *time.Time `db:"order_create_time" json:"order_create_time"`
	PurchaseOrderCode string     `db:"purchase_order_code" json:"purchase_order_code"`
	ExpectStartTime   *time.Time `db:"expect_start_time" json:"expect_start_time"`
	ExpectEndTime     *time.Time `db:"expect_end_time" json:"expect_end_time"`
	ActualArrivalTime *time.Time `db:"actual_arrival_time" json:"actual_arrival_time"`
	LogisticsCode     string     `db:"logistics_code" json:"logistics_code"`
	LogisticsName     string     `db:"logistics_name" json:"logistics_name"`
	ExpressCode       string     `db:"express_code" json:"express_code"`
	SupplierCode      string     `db:"supplier_code" json:"supplier_code"`
	SupplierName      string     `db:"supplier_name" json:"supplier_name"`
	OperatorCode      string     `db:"operator_code" json:"operator_code"`
	OperatorName      string     `db:"operator_name" json:"operator_name"`
	OperateTime       *time.Time `db:"operate_time" json:"operate_time"`
	TotalOrderLines   int32      `db:"total_order_lines" json:"total_order_lines"`
	ExpectedQuantity  int32      `db:"expected_quantity" json:"expected_quantity"`
	ActualQuantity    int32      `db:"actual_quantity" json:"actual_quantity"`
	OrderStatus       int32      `db:"order_status" json:"order_status"`
	Remark            string     `db:"remark" json:"remark"`
	ExtendProps       JSONMap    `db:"extend_props" json:"extend_props"`
	QimenEntryOrderID string     `db:"qimen_entry_order_id" json:"qimen_entry_order_id"`
	ApiSyncStatus     int32      `db:"api_sync_status" json:"api_sync_status"`
	ApiSyncTime       *time.Time `db:"api_sync_time" json:"api_sync_time"`
	ApiErrorMessage   string     `db:"api_error_message" json:"api_error_message"`
	CreatedAt         time.Time  `db:"created_at" json:"created_at"`
	UpdatedAt         time.Time  `db:"updated_at" json:"updated_at"`
}

// EntryOrderLine 入库单明细模型
type EntryOrderLine struct {
	ID             int64      `db:"id" json:"id"`
	EntryOrderID   int64      `db:"entry_order_id" json:"entry_order_id"`
	EntryOrderCode string     `db:"entry_order_code" json:"entry_order_code"`
	OrderLineNo    string     `db:"order_line_no" json:"order_line_no"`
	OutBizCode     string     `db:"out_biz_code" json:"out_biz_code"`
	OwnerCode      string     `db:"owner_code" json:"owner_code"`
	ItemCode       string     `db:"item_code" json:"item_code"`
	ItemID         string     `db:"item_id" json:"item_id"`
	ItemName       string     `db:"item_name" json:"item_name"`
	SkuProperty    string     `db:"sku_property" json:"sku_property"`
	PlanQty        int32      `db:"plan_qty" json:"plan_qty"`
	ActualQty      int32      `db:"actual_qty" json:"actual_qty"`
	PurchasePrice  float64    `db:"purchase_price" json:"purchase_price"`
	RetailPrice    float64    `db:"retail_price" json:"retail_price"`
	InventoryType  string     `db:"inventory_type" json:"inventory_type"`
	BatchCode      string     `db:"batch_code" json:"batch_code"`
	ProduceCode    string     `db:"produce_code" json:"produce_code"`
	ProductDate    *time.Time `db:"product_date" json:"product_date"`
	ExpireDate     *time.Time `db:"expire_date" json:"expire_date"`
	BoxNumber      string     `db:"box_number" json:"box_number"`
	PalletNumber   string     `db:"pallet_number" json:"pallet_number"`
	Unit           string     `db:"unit" json:"unit"`
	SnCodes        JSONArray  `db:"sn_codes" json:"sn_codes"`
	ShelfLocation  string     `db:"shelf_location" json:"shelf_location"`
	InboundTime    *time.Time `db:"inbound_time" json:"inbound_time"`
	DeviceStatus   int32      `db:"device_status" json:"device_status"`
	Remark         string     `db:"remark" json:"remark"`
	ExtendProps    JSONMap    `db:"extend_props" json:"extend_props"`
	CreatedAt      time.Time  `db:"created_at" json:"created_at"`
}

// JSONMap 用于处理JSONB字段
type JSONMap map[string]interface{}

// Scan 实现 sql.Scanner 接口
func (j *JSONMap) Scan(value interface{}) error {
	if value == nil {
		*j = make(JSONMap)
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into JSONMap", value)
	}

	if len(bytes) == 0 {
		*j = make(JSONMap)
		return nil
	}

	return json.Unmarshal(bytes, j)
}

// Value 实现 driver.Valuer 接口
func (j JSONMap) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// JSONArray 用于处理JSONB数组字段
type JSONArray []string

// Scan 实现 sql.Scanner 接口
func (j *JSONArray) Scan(value interface{}) error {
	if value == nil {
		*j = make(JSONArray, 0)
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into JSONArray", value)
	}

	if len(bytes) == 0 {
		*j = make(JSONArray, 0)
		return nil
	}

	return json.Unmarshal(bytes, j)
}

// Value 实现 driver.Valuer 接口
func (j JSONArray) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// EntryOrderRepo 入库单仓储
type EntryOrderRepo struct {
	db     *pgxpool.Pool
	logger logx.Logger
}

// NewEntryOrderRepo 创建入库单仓储
func NewEntryOrderRepo(db *pgxpool.Pool) *EntryOrderRepo {
	return &EntryOrderRepo{
		db:     db,
		logger: logx.WithContext(context.Background()),
	}
}

// Create 创建入库单
func (r *EntryOrderRepo) Create(ctx context.Context, entryOrder *EntryOrder) error {
	// 验证参数
	if err := r.validateEntryOrder(entryOrder); err != nil {
		return err
	}

	// 生成入库单号
	if entryOrder.EntryOrderCode == "" {
		entryOrder.EntryOrderCode = utils.GenerateEntryOrderCode()
	}

	// 设置默认值
	if entryOrder.OwnerCode == "" {
		entryOrder.OwnerCode = constants.DefaultOwnerCode
	}
	if entryOrder.OrderType == "" {
		entryOrder.OrderType = constants.OrderTypeCGRK
	}
	if entryOrder.OrderStatus == 0 {
		entryOrder.OrderStatus = constants.EntryOrderStatusPending
	}
	if entryOrder.ApiSyncStatus == 0 {
		entryOrder.ApiSyncStatus = constants.ApiSyncStatusNotSynced
	}

	sql := `
		INSERT INTO iot.warehouse_entry_orders (
			entry_order_code, warehouse_code, warehouse_name, owner_code, order_type,
			order_create_time, purchase_order_code, expect_start_time, expect_end_time,
			actual_arrival_time, logistics_code, logistics_name, express_code,
			supplier_code, supplier_name, operator_code, operator_name, operate_time,
			total_order_lines, expected_quantity, actual_quantity, order_status,
			remark, extend_props, qimen_entry_order_id, api_sync_status,
			api_sync_time, api_error_message, created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10,
			$11, $12, $13, $14, $15, $16, $17, $18, $19, $20,
			$21, $22, $23, $24, $25, $26, $27, $28, NOW(), NOW()
		) RETURNING id, created_at, updated_at`

	err := r.db.QueryRow(ctx, sql,
		entryOrder.EntryOrderCode, entryOrder.WarehouseCode, entryOrder.WarehouseName,
		entryOrder.OwnerCode, entryOrder.OrderType, entryOrder.OrderCreateTime,
		entryOrder.PurchaseOrderCode, entryOrder.ExpectStartTime, entryOrder.ExpectEndTime,
		entryOrder.ActualArrivalTime, entryOrder.LogisticsCode, entryOrder.LogisticsName,
		entryOrder.ExpressCode, entryOrder.SupplierCode, entryOrder.SupplierName,
		entryOrder.OperatorCode, entryOrder.OperatorName, entryOrder.OperateTime,
		entryOrder.TotalOrderLines, entryOrder.ExpectedQuantity, entryOrder.ActualQuantity,
		entryOrder.OrderStatus, entryOrder.Remark, entryOrder.ExtendProps,
		entryOrder.QimenEntryOrderID, entryOrder.ApiSyncStatus, entryOrder.ApiSyncTime,
		entryOrder.ApiErrorMessage,
	).Scan(&entryOrder.ID, &entryOrder.CreatedAt, &entryOrder.UpdatedAt)

	if err != nil {
		r.logger.Errorf("创建入库单失败: %v", err)
		return errors.ErrDatabaseError.WithDetail(err.Error())
	}

	r.logger.Infof("创建入库单成功: ID=%d, Code=%s", entryOrder.ID, entryOrder.EntryOrderCode)
	return nil
}

// GetByID 根据ID获取入库单
func (r *EntryOrderRepo) GetByID(ctx context.Context, id int64) (*EntryOrder, error) {
	if id <= 0 {
		return nil, errors.ErrInvalidParam.WithDetail("入库单ID无效")
	}

	sql := `
		SELECT id, entry_order_code, warehouse_code, warehouse_name, owner_code,
			   order_type, order_create_time, purchase_order_code, expect_start_time,
			   expect_end_time, actual_arrival_time, logistics_code, logistics_name,
			   express_code, supplier_code, supplier_name, operator_code, operator_name,
			   operate_time, total_order_lines, expected_quantity, actual_quantity,
			   order_status, remark, extend_props, qimen_entry_order_id, api_sync_status,
			   api_sync_time, api_error_message, created_at, updated_at
		FROM iot.warehouse_entry_orders 
		WHERE id = $1`

	var entryOrder EntryOrder
	err := pgxscan.Get(ctx, r.db, &entryOrder, sql, id)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.ErrEntryOrderNotFound
		}
		r.logger.Errorf("查询入库单失败: %v", err)
		return nil, errors.ErrDatabaseError.WithDetail(err.Error())
	}

	return &entryOrder, nil
}

// GetByCode 根据编码获取入库单
func (r *EntryOrderRepo) GetByCode(ctx context.Context, code string) (*EntryOrder, error) {
	if code == "" {
		return nil, errors.ErrInvalidParam.WithDetail("入库单编码不能为空")
	}

	sql := `
		SELECT id, entry_order_code, warehouse_code, warehouse_name, owner_code,
			   order_type, order_create_time, purchase_order_code, expect_start_time,
			   expect_end_time, actual_arrival_time, logistics_code, logistics_name,
			   express_code, supplier_code, supplier_name, operator_code, operator_name,
			   operate_time, total_order_lines, expected_quantity, actual_quantity,
			   order_status, remark, extend_props, qimen_entry_order_id, api_sync_status,
			   api_sync_time, api_error_message, created_at, updated_at
		FROM iot.warehouse_entry_orders 
		WHERE entry_order_code = $1`

	var entryOrder EntryOrder
	err := pgxscan.Get(ctx, r.db, &entryOrder, sql, code)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.ErrEntryOrderNotFound
		}
		r.logger.Errorf("查询入库单失败: %v", err)
		return nil, errors.ErrDatabaseError.WithDetail(err.Error())
	}

	return &entryOrder, nil
}

// validateEntryOrder 验证入库单数据
func (r *EntryOrderRepo) validateEntryOrder(entryOrder *EntryOrder) error {
	if entryOrder == nil {
		return errors.ErrInvalidParam.WithDetail("入库单信息不能为空")
	}
	if entryOrder.WarehouseCode == "" {
		return errors.ErrInvalidParam.WithDetail("云仓编码不能为空")
	}
	if entryOrder.SupplierCode == "" {
		return errors.ErrInvalidParam.WithDetail("供应商编码不能为空")
	}
	if entryOrder.SupplierName == "" {
		return errors.ErrInvalidParam.WithDetail("供应商名称不能为空")
	}

	return nil
}

// EntryOrderLineRepo 入库单明细仓储
type EntryOrderLineRepo struct {
	db     *pgxpool.Pool
	logger logx.Logger
}

// NewEntryOrderLineRepo 创建入库单明细仓储
func NewEntryOrderLineRepo(db *pgxpool.Pool) *EntryOrderLineRepo {
	return &EntryOrderLineRepo{
		db:     db,
		logger: logx.WithContext(context.Background()),
	}
}

// CreateLines 批量创建入库单明细
func (r *EntryOrderLineRepo) CreateLines(ctx context.Context, lines []*EntryOrderLine) error {
	if len(lines) == 0 {
		return errors.ErrInvalidParam.WithDetail("入库明细不能为空")
	}

	// 开启事务
	tx, err := r.db.Begin(ctx)
	if err != nil {
		r.logger.Errorf("开启事务失败: %v", err)
		return errors.ErrDatabaseError.WithDetail(err.Error())
	}
	defer tx.Rollback(ctx)

	sql := `
		INSERT INTO iot.warehouse_entry_order_lines (
			entry_order_id, entry_order_code, order_line_no, out_biz_code, owner_code,
			item_code, item_id, item_name, sku_property, plan_qty, actual_qty,
			purchase_price, retail_price, inventory_type, batch_code, produce_code,
			product_date, expire_date, box_number, pallet_number, unit, sn_codes,
			shelf_location, inbound_time, device_status, remark, extend_props, created_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10,
			$11, $12, $13, $14, $15, $16, $17, $18, $19, $20,
			$21, $22, $23, $24, $25, $26, $27, NOW()
		) RETURNING id`

	for i, line := range lines {
		// 验证明细
		if err := r.validateEntryOrderLine(line); err != nil {
			return err
		}

		// 生成行号
		if line.OrderLineNo == "" {
			line.OrderLineNo = utils.GenerateOrderLineNo(line.EntryOrderCode, i+1)
		}

		// 生成外部业务编码
		if line.OutBizCode == "" {
			line.OutBizCode = utils.GenerateOutBizCode(line.EntryOrderCode, line.ItemCode)
		}

		// 设置默认值
		if line.OwnerCode == "" {
			line.OwnerCode = constants.DefaultOwnerCode
		}
		if line.Unit == "" {
			line.Unit = constants.DefaultUnit
		}
		if line.InventoryType == "" {
			line.InventoryType = constants.InventoryTypeZP
		}
		if line.DeviceStatus == 0 {
			line.DeviceStatus = constants.DeviceStatusNormal
		}

		err := tx.QueryRow(ctx, sql,
			line.EntryOrderID, line.EntryOrderCode, line.OrderLineNo, line.OutBizCode,
			line.OwnerCode, line.ItemCode, line.ItemID, line.ItemName, line.SkuProperty,
			line.PlanQty, line.ActualQty, line.PurchasePrice, line.RetailPrice,
			line.InventoryType, line.BatchCode, line.ProduceCode, line.ProductDate,
			line.ExpireDate, line.BoxNumber, line.PalletNumber, line.Unit, line.SnCodes,
			line.ShelfLocation, line.InboundTime, line.DeviceStatus, line.Remark,
			line.ExtendProps,
		).Scan(&line.ID)

		if err != nil {
			r.logger.Errorf("创建入库明细失败: %v", err)
			return errors.ErrDatabaseError.WithDetail(err.Error())
		}
	}

	// 提交事务
	if err := tx.Commit(ctx); err != nil {
		r.logger.Errorf("提交事务失败: %v", err)
		return errors.ErrDatabaseError.WithDetail(err.Error())
	}

	r.logger.Infof("批量创建入库明细成功: 数量=%d", len(lines))
	return nil
}

// GetLinesByOrderID 根据入库单ID获取明细列表
func (r *EntryOrderLineRepo) GetLinesByOrderID(ctx context.Context, entryOrderID int64) ([]*EntryOrderLine, error) {
	if entryOrderID <= 0 {
		return nil, errors.ErrInvalidParam.WithDetail("入库单ID无效")
	}

	sql := `
		SELECT id, entry_order_id, entry_order_code, order_line_no, out_biz_code,
			   owner_code, item_code, item_id, item_name, sku_property, plan_qty,
			   actual_qty, purchase_price, retail_price, inventory_type, batch_code,
			   produce_code, product_date, expire_date, box_number, pallet_number,
			   unit, sn_codes, shelf_location, inbound_time, device_status, remark,
			   extend_props, created_at
		FROM iot.warehouse_entry_order_lines
		WHERE entry_order_id = $1
		ORDER BY order_line_no`

	var lines []*EntryOrderLine
	err := pgxscan.Select(ctx, r.db, &lines, sql, entryOrderID)
	if err != nil {
		r.logger.Errorf("查询入库明细失败: %v", err)
		return nil, errors.ErrDatabaseError.WithDetail(err.Error())
	}

	return lines, nil
}

// UpdateLine 更新入库单明细
func (r *EntryOrderLineRepo) UpdateLine(ctx context.Context, line *EntryOrderLine) error {
	if line.ID <= 0 {
		return errors.ErrInvalidParam.WithDetail("入库明细ID无效")
	}

	sql := `
		UPDATE iot.warehouse_entry_order_lines
		SET actual_qty = $2, shelf_location = $3, inbound_time = $4,
			device_status = $5, remark = $6
		WHERE id = $1`

	result, err := r.db.Exec(ctx, sql, line.ID, line.ActualQty, line.ShelfLocation,
		line.InboundTime, line.DeviceStatus, line.Remark)
	if err != nil {
		r.logger.Errorf("更新入库明细失败: %v", err)
		return errors.ErrDatabaseError.WithDetail(err.Error())
	}

	if result.RowsAffected() == 0 {
		return errors.ErrEntryOrderNotFound.WithDetail("入库明细不存在")
	}

	r.logger.Infof("更新入库明细成功: ID=%d", line.ID)
	return nil
}

// validateEntryOrderLine 验证入库单明细数据
func (r *EntryOrderLineRepo) validateEntryOrderLine(line *EntryOrderLine) error {
	if line == nil {
		return errors.ErrInvalidParam.WithDetail("入库明细信息不能为空")
	}
	if line.EntryOrderID <= 0 {
		return errors.ErrInvalidParam.WithDetail("入库单ID无效")
	}
	if line.EntryOrderCode == "" {
		return errors.ErrInvalidParam.WithDetail("入库单编码不能为空")
	}
	if !utils.ValidateItemCode(line.ItemCode) {
		return errors.ErrInvalidParam.WithDetail("商品编码格式无效")
	}
	if line.ItemName == "" {
		return errors.ErrInvalidParam.WithDetail("商品名称不能为空")
	}
	if line.PlanQty <= 0 {
		return errors.ErrInvalidParam.WithDetail("计划数量必须大于0")
	}

	return nil
}

// EntryOrderListQuery 入库单列表查询参数
type EntryOrderListQuery struct {
	Page          int64  `json:"page"`
	PageSize      int64  `json:"page_size"`
	WarehouseCode string `json:"warehouse_code"`
	OrderStatus   int32  `json:"order_status"`
	SupplierCode  string `json:"supplier_code"`
	StartTime     string `json:"start_time"`
	EndTime       string `json:"end_time"`
	Keyword       string `json:"keyword"`
}

// EntryOrderListResult 入库单列表查询结果
type EntryOrderListResult struct {
	List  []*EntryOrder `json:"list"`
	Total int64         `json:"total"`
}

// GetList 获取入库单列表
func (r *EntryOrderRepo) GetList(ctx context.Context, query *EntryOrderListQuery) (*EntryOrderListResult, error) {
	// 验证分页参数
	if query.Page <= 0 {
		query.Page = 1
	}
	if query.PageSize <= 0 || query.PageSize > 100 {
		query.PageSize = 20
	}

	// 构建查询条件
	whereParts := []string{}
	args := []interface{}{}
	argIndex := 1

	// 云仓编码筛选
	if query.WarehouseCode != "" {
		whereParts = append(whereParts, fmt.Sprintf("warehouse_code = $%d", argIndex))
		args = append(args, query.WarehouseCode)
		argIndex++
	}

	// 订单状态筛选
	if query.OrderStatus > 0 {
		whereParts = append(whereParts, fmt.Sprintf("order_status = $%d", argIndex))
		args = append(args, query.OrderStatus)
		argIndex++
	}

	// 供应商编码筛选
	if query.SupplierCode != "" {
		whereParts = append(whereParts, fmt.Sprintf("supplier_code = $%d", argIndex))
		args = append(args, query.SupplierCode)
		argIndex++
	}

	// 时间范围筛选
	if query.StartTime != "" {
		if startTime, parseErr := utils.ParseTimestamp(query.StartTime); parseErr == nil {
			whereParts = append(whereParts, fmt.Sprintf("created_at >= $%d", argIndex))
			args = append(args, time.Unix(startTime, 0))
			argIndex++
		}
	}
	if query.EndTime != "" {
		if endTime, parseErr := utils.ParseTimestamp(query.EndTime); parseErr == nil {
			whereParts = append(whereParts, fmt.Sprintf("created_at <= $%d", argIndex))
			args = append(args, time.Unix(endTime, 0))
			argIndex++
		}
	}

	// 关键词搜索
	if query.Keyword != "" {
		whereParts = append(whereParts, fmt.Sprintf("(entry_order_code ILIKE $%d OR supplier_name ILIKE $%d)", argIndex, argIndex))
		args = append(args, "%"+query.Keyword+"%")
		argIndex++
	}

	whereClause := ""
	if len(whereParts) > 0 {
		whereClause = "WHERE " + strings.Join(whereParts, " AND ")
	}

	// 查询总数
	countSQL := fmt.Sprintf("SELECT COUNT(*) FROM iot.warehouse_entry_orders %s", whereClause)
	var total int64
	err := r.db.QueryRow(ctx, countSQL, args...).Scan(&total)
	if err != nil {
		r.logger.Errorf("查询入库单总数失败: %v", err)
		return nil, errors.ErrDatabaseError.WithDetail(err.Error())
	}

	// 查询列表
	offset := (query.Page - 1) * query.PageSize
	listSQL := fmt.Sprintf(`
		SELECT id, entry_order_code, warehouse_code, warehouse_name, owner_code,
			   order_type, order_create_time, purchase_order_code, expect_start_time,
			   expect_end_time, actual_arrival_time, logistics_code, logistics_name,
			   express_code, supplier_code, supplier_name, operator_code, operator_name,
			   operate_time, total_order_lines, expected_quantity, actual_quantity,
			   order_status, remark, extend_props, qimen_entry_order_id, api_sync_status,
			   api_sync_time, api_error_message, created_at, updated_at
		FROM iot.warehouse_entry_orders
		%s
		ORDER BY created_at DESC
		LIMIT $%d OFFSET $%d`, whereClause, argIndex, argIndex+1)

	args = append(args, query.PageSize, offset)

	var entryOrders []*EntryOrder
	err = pgxscan.Select(ctx, r.db, &entryOrders, listSQL, args...)
	if err != nil {
		r.logger.Errorf("查询入库单列表失败: %v", err)
		return nil, errors.ErrDatabaseError.WithDetail(err.Error())
	}

	return &EntryOrderListResult{
		List:  entryOrders,
		Total: total,
	}, nil
}

// UpdateStatus 更新入库单状态
func (r *EntryOrderRepo) UpdateStatus(ctx context.Context, id int64, status int32, operatorName, remark string) error {
	if id <= 0 {
		return errors.ErrInvalidParam.WithDetail("入库单ID无效")
	}

	// 验证状态转换是否合法
	currentOrder, err := r.GetByID(ctx, id)
	if err != nil {
		return err
	}

	if !utils.CanTransitionOrderStatus("entry", currentOrder.OrderStatus, status) {
		return errors.ErrOrderStatusInvalid.WithDetail(fmt.Sprintf("不能从状态%d转换到状态%d", currentOrder.OrderStatus, status))
	}

	sql := `
		UPDATE iot.warehouse_entry_orders
		SET order_status = $2, operator_name = $3, operate_time = NOW(),
			remark = $4, updated_at = NOW()
		WHERE id = $1`

	result, err := r.db.Exec(ctx, sql, id, status, operatorName, remark)
	if err != nil {
		r.logger.Errorf("更新入库单状态失败: %v", err)
		return errors.ErrDatabaseError.WithDetail(err.Error())
	}

	if result.RowsAffected() == 0 {
		return errors.ErrEntryOrderNotFound
	}

	r.logger.Infof("更新入库单状态成功: ID=%d, Status=%d", id, status)
	return nil
}

// UpdateActualArrivalTime 更新实际到货时间
func (r *EntryOrderRepo) UpdateActualArrivalTime(ctx context.Context, id int64, arrivalTime time.Time) error {
	if id <= 0 {
		return errors.ErrInvalidParam.WithDetail("入库单ID无效")
	}

	sql := `
		UPDATE iot.warehouse_entry_orders
		SET actual_arrival_time = $2, updated_at = NOW()
		WHERE id = $1`

	result, err := r.db.Exec(ctx, sql, id, arrivalTime)
	if err != nil {
		r.logger.Errorf("更新入库单到货时间失败: %v", err)
		return errors.ErrDatabaseError.WithDetail(err.Error())
	}

	if result.RowsAffected() == 0 {
		return errors.ErrEntryOrderNotFound
	}

	r.logger.Infof("更新入库单到货时间成功: ID=%d", id)
	return nil
}

// UpdateQuantities 更新入库单数量统计
func (r *EntryOrderRepo) UpdateQuantities(ctx context.Context, id int64) error {
	if id <= 0 {
		return errors.ErrInvalidParam.WithDetail("入库单ID无效")
	}

	// 计算实际入库数量
	sql := `
		UPDATE iot.warehouse_entry_orders
		SET actual_quantity = (
			SELECT COALESCE(SUM(actual_qty), 0)
			FROM iot.warehouse_entry_order_lines
			WHERE entry_order_id = $1
		), updated_at = NOW()
		WHERE id = $1`

	result, err := r.db.Exec(ctx, sql, id)
	if err != nil {
		r.logger.Errorf("更新入库单数量统计失败: %v", err)
		return errors.ErrDatabaseError.WithDetail(err.Error())
	}

	if result.RowsAffected() == 0 {
		return errors.ErrEntryOrderNotFound
	}

	r.logger.Infof("更新入库单数量统计成功: ID=%d", id)
	return nil
}
