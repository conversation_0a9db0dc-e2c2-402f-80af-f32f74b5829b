package model

import (
	"context"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type Card struct {
	ID             int64     `json:"id"`
	ICCID          string    `json:"iccid"`
	MSISDN         string    `json:"msisdn"`
	IMSI           string    `json:"imsi"`
	CreatedAt      time.Time `json:"created_at"`
	Status         int       `json:"status"`
	Tag            []string  `json:"tag"`
	AddedDate      time.Time `json:"added_date"`
	UpdatedAt      time.Time `json:"updated_at"`
	LockStatus     int       `json:"lock_status"`
	ActivatedAt    time.Time `json:"activated_at"`
	FixedIPAddress string    `json:"fixed_ip_address"`
	ChannelID      int64     `json:"channel_id"`
	CostPrice      float64   `json:"cost_price"`
}

type CardRepo struct {
	db *pgxpool.Pool
}

func NewCardRepo(db *pgxpool.Pool) *CardRepo {
	return &CardRepo{db: db}
}

// 创建简单卡片
func (r *CardRepo) CreateSimpleCard(ctx context.Context, iccid, msisdn, imsi string, addTime time.Time, channelID int64, costPrice float64, tags []string, cover bool) (int64, error) {
	// 方案2：简化的查询逻辑
	query := `
		INSERT INTO iot.cards (iccid, msisdn, imsi, status, tag, added_date, channel_id, cost_price)
		VALUES (@iccid, @msisdn, @imsi, @status, COALESCE(@tag, ARRAY[]::text[]), @added_date, @channel_id, @cost_price) RETURNING id
	`
	row := r.db.QueryRow(ctx, query, pgx.NamedArgs{
		"iccid":      iccid,
		"msisdn":     msisdn,
		"imsi":       imsi,
		"status":     1,
		"tag":        tags,
		"added_date": addTime,
		"channel_id": channelID,
		"cost_price": costPrice,
	})
	var id int64
	err := row.Scan(&id)
	if err != nil {
		return 0, fmt.Errorf("failed to create or update card: %w", err)
	}
	return id, nil
}
