package model

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/Wenpiner/iot-api/pkg/constants"
	"github.com/Wenpiner/iot-api/pkg/errors"
	"github.com/georgysavva/scany/v2/pgxscan"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/zeromicro/go-zero/core/logx"
)

// QimenSyncLog 奇门同步日志模型
type QimenSyncLog struct {
	ID            int64     `db:"id" json:"id"`
	SyncType      int32     `db:"sync_type" json:"sync_type"`
	SyncDirection int32     `db:"sync_direction" json:"sync_direction"`
	BusinessID    int64     `db:"business_id" json:"business_id"`
	BusinessCode  string    `db:"business_code" json:"business_code"`
	WarehouseCode string    `db:"warehouse_code" json:"warehouse_code"`
	ApiMethod     string    `db:"api_method" json:"api_method"`
	RequestData   string    `db:"request_data" json:"request_data"`
	ResponseData  string    `db:"response_data" json:"response_data"`
	SyncStatus    int32     `db:"sync_status" json:"sync_status"`
	ErrorMessage  string    `db:"error_message" json:"error_message"`
	RetryCount    int32     `db:"retry_count" json:"retry_count"`
	NextRetryTime *time.Time `db:"next_retry_time" json:"next_retry_time"`
	SyncTime      time.Time `db:"sync_time" json:"sync_time"`
	CreatedAt     time.Time `db:"created_at" json:"created_at"`
}

// QimenSyncLogRepo 奇门同步日志仓储
type QimenSyncLogRepo struct {
	db     *pgxpool.Pool
	logger logx.Logger
}

// NewQimenSyncLogRepo 创建奇门同步日志仓储
func NewQimenSyncLogRepo(db *pgxpool.Pool) *QimenSyncLogRepo {
	return &QimenSyncLogRepo{
		db:     db,
		logger: logx.WithContext(context.Background()),
	}
}

// Create 创建同步日志
func (r *QimenSyncLogRepo) Create(ctx context.Context, log *QimenSyncLog) error {
	// 验证参数
	if err := r.validateSyncLog(log); err != nil {
		return err
	}

	sql := `
		INSERT INTO iot.qimen_sync_logs (
			sync_type, sync_direction, business_id, business_code, warehouse_code,
			api_method, request_data, response_data, sync_status, error_message,
			retry_count, next_retry_time, sync_time, created_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10,
			$11, $12, $13, NOW()
		) RETURNING id, created_at`

	err := r.db.QueryRow(ctx, sql,
		log.SyncType, log.SyncDirection, log.BusinessID, log.BusinessCode,
		log.WarehouseCode, log.ApiMethod, log.RequestData, log.ResponseData,
		log.SyncStatus, log.ErrorMessage, log.RetryCount, log.NextRetryTime,
		log.SyncTime,
	).Scan(&log.ID, &log.CreatedAt)

	if err != nil {
		r.logger.Errorf("创建同步日志失败: %v", err)
		return errors.ErrDatabaseError.WithDetail(err.Error())
	}

	r.logger.Infof("创建同步日志成功: ID=%d, BusinessCode=%s", log.ID, log.BusinessCode)
	return nil
}

// GetByID 根据ID获取同步日志
func (r *QimenSyncLogRepo) GetByID(ctx context.Context, id int64) (*QimenSyncLog, error) {
	if id <= 0 {
		return nil, errors.ErrInvalidParam.WithDetail("同步日志ID无效")
	}

	sql := `
		SELECT id, sync_type, sync_direction, business_id, business_code,
			   warehouse_code, api_method, request_data, response_data,
			   sync_status, error_message, retry_count, next_retry_time,
			   sync_time, created_at
		FROM iot.qimen_sync_logs 
		WHERE id = $1`

	var log QimenSyncLog
	err := pgxscan.Get(ctx, r.db, &log, sql, id)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.ErrInvalidParam.WithDetail("同步日志不存在")
		}
		r.logger.Errorf("查询同步日志失败: %v", err)
		return nil, errors.ErrDatabaseError.WithDetail(err.Error())
	}

	return &log, nil
}

// GetByBusinessCode 根据业务编码获取最新同步日志
func (r *QimenSyncLogRepo) GetByBusinessCode(ctx context.Context, businessCode string, syncType int32) (*QimenSyncLog, error) {
	if businessCode == "" {
		return nil, errors.ErrInvalidParam.WithDetail("业务编码不能为空")
	}

	sql := `
		SELECT id, sync_type, sync_direction, business_id, business_code,
			   warehouse_code, api_method, request_data, response_data,
			   sync_status, error_message, retry_count, next_retry_time,
			   sync_time, created_at
		FROM iot.qimen_sync_logs 
		WHERE business_code = $1 AND sync_type = $2
		ORDER BY created_at DESC 
		LIMIT 1`

	var log QimenSyncLog
	err := pgxscan.Get(ctx, r.db, &log, sql, businessCode, syncType)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, errors.ErrInvalidParam.WithDetail("同步日志不存在")
		}
		r.logger.Errorf("查询同步日志失败: %v", err)
		return nil, errors.ErrDatabaseError.WithDetail(err.Error())
	}

	return &log, nil
}

// UpdateStatus 更新同步状态
func (r *QimenSyncLogRepo) UpdateStatus(ctx context.Context, id int64, status int32, responseData, errorMessage string) error {
	if id <= 0 {
		return errors.ErrInvalidParam.WithDetail("同步日志ID无效")
	}

	sql := `
		UPDATE iot.qimen_sync_logs 
		SET sync_status = $2, response_data = $3, error_message = $4, sync_time = NOW()
		WHERE id = $1`

	result, err := r.db.Exec(ctx, sql, id, status, responseData, errorMessage)
	if err != nil {
		r.logger.Errorf("更新同步状态失败: %v", err)
		return errors.ErrDatabaseError.WithDetail(err.Error())
	}

	if result.RowsAffected() == 0 {
		return errors.ErrInvalidParam.WithDetail("同步日志不存在")
	}

	r.logger.Infof("更新同步状态成功: ID=%d, Status=%d", id, status)
	return nil
}

// UpdateRetryInfo 更新重试信息
func (r *QimenSyncLogRepo) UpdateRetryInfo(ctx context.Context, id int64, retryCount int32, nextRetryTime *time.Time) error {
	if id <= 0 {
		return errors.ErrInvalidParam.WithDetail("同步日志ID无效")
	}

	sql := `
		UPDATE iot.qimen_sync_logs 
		SET retry_count = $2, next_retry_time = $3
		WHERE id = $1`

	result, err := r.db.Exec(ctx, sql, id, retryCount, nextRetryTime)
	if err != nil {
		r.logger.Errorf("更新重试信息失败: %v", err)
		return errors.ErrDatabaseError.WithDetail(err.Error())
	}

	if result.RowsAffected() == 0 {
		return errors.ErrInvalidParam.WithDetail("同步日志不存在")
	}

	r.logger.Infof("更新重试信息成功: ID=%d, RetryCount=%d", id, retryCount)
	return nil
}

// QimenSyncLogListQuery 同步日志列表查询参数
type QimenSyncLogListQuery struct {
	Page          int64  `json:"page"`
	PageSize      int64  `json:"page_size"`
	SyncType      int32  `json:"sync_type"`
	SyncDirection int32  `json:"sync_direction"`
	SyncStatus    int32  `json:"sync_status"`
	WarehouseCode string `json:"warehouse_code"`
	BusinessCode  string `json:"business_code"`
	StartTime     string `json:"start_time"`
	EndTime       string `json:"end_time"`
}

// QimenSyncLogListResult 同步日志列表查询结果
type QimenSyncLogListResult struct {
	List  []*QimenSyncLog `json:"list"`
	Total int64           `json:"total"`
}

// GetList 获取同步日志列表
func (r *QimenSyncLogRepo) GetList(ctx context.Context, query *QimenSyncLogListQuery) (*QimenSyncLogListResult, error) {
	// 验证分页参数
	if query.Page <= 0 {
		query.Page = 1
	}
	if query.PageSize <= 0 || query.PageSize > 100 {
		query.PageSize = 20
	}

	// 构建查询条件
	whereParts := []string{}
	args := []interface{}{}
	argIndex := 1

	// 同步类型筛选
	if query.SyncType > 0 {
		whereParts = append(whereParts, fmt.Sprintf("sync_type = $%d", argIndex))
		args = append(args, query.SyncType)
		argIndex++
	}

	// 同步方向筛选
	if query.SyncDirection > 0 {
		whereParts = append(whereParts, fmt.Sprintf("sync_direction = $%d", argIndex))
		args = append(args, query.SyncDirection)
		argIndex++
	}

	// 同步状态筛选
	if query.SyncStatus > 0 {
		whereParts = append(whereParts, fmt.Sprintf("sync_status = $%d", argIndex))
		args = append(args, query.SyncStatus)
		argIndex++
	}

	// 云仓编码筛选
	if query.WarehouseCode != "" {
		whereParts = append(whereParts, fmt.Sprintf("warehouse_code = $%d", argIndex))
		args = append(args, query.WarehouseCode)
		argIndex++
	}

	// 业务编码筛选
	if query.BusinessCode != "" {
		whereParts = append(whereParts, fmt.Sprintf("business_code ILIKE $%d", argIndex))
		args = append(args, "%"+query.BusinessCode+"%")
		argIndex++
	}

	whereClause := ""
	if len(whereParts) > 0 {
		whereClause = "WHERE " + strings.Join(whereParts, " AND ")
	}

	// 查询总数
	countSQL := fmt.Sprintf("SELECT COUNT(*) FROM iot.qimen_sync_logs %s", whereClause)
	var total int64
	err := r.db.QueryRow(ctx, countSQL, args...).Scan(&total)
	if err != nil {
		r.logger.Errorf("查询同步日志总数失败: %v", err)
		return nil, errors.ErrDatabaseError.WithDetail(err.Error())
	}

	// 查询列表
	offset := (query.Page - 1) * query.PageSize
	listSQL := fmt.Sprintf(`
		SELECT id, sync_type, sync_direction, business_id, business_code,
			   warehouse_code, api_method, request_data, response_data,
			   sync_status, error_message, retry_count, next_retry_time,
			   sync_time, created_at
		FROM iot.qimen_sync_logs 
		%s 
		ORDER BY created_at DESC 
		LIMIT $%d OFFSET $%d`, whereClause, argIndex, argIndex+1)

	args = append(args, query.PageSize, offset)

	var logs []*QimenSyncLog
	err = pgxscan.Select(ctx, r.db, &logs, listSQL, args...)
	if err != nil {
		r.logger.Errorf("查询同步日志列表失败: %v", err)
		return nil, errors.ErrDatabaseError.WithDetail(err.Error())
	}

	return &QimenSyncLogListResult{
		List:  logs,
		Total: total,
	}, nil
}

// GetFailedLogs 获取需要重试的失败日志
func (r *QimenSyncLogRepo) GetFailedLogs(ctx context.Context, limit int32) ([]*QimenSyncLog, error) {
	if limit <= 0 || limit > 100 {
		limit = 50
	}

	sql := `
		SELECT id, sync_type, sync_direction, business_id, business_code,
			   warehouse_code, api_method, request_data, response_data,
			   sync_status, error_message, retry_count, next_retry_time,
			   sync_time, created_at
		FROM iot.qimen_sync_logs 
		WHERE sync_status = $1 
		  AND retry_count < 3 
		  AND (next_retry_time IS NULL OR next_retry_time <= NOW())
		ORDER BY created_at ASC 
		LIMIT $2`

	var logs []*QimenSyncLog
	err := pgxscan.Select(ctx, r.db, &logs, sql, constants.QimenSyncStatusFailed, limit)
	if err != nil {
		r.logger.Errorf("查询失败日志失败: %v", err)
		return nil, errors.ErrDatabaseError.WithDetail(err.Error())
	}

	return logs, nil
}

// validateSyncLog 验证同步日志数据
func (r *QimenSyncLogRepo) validateSyncLog(log *QimenSyncLog) error {
	if log == nil {
		return errors.ErrInvalidParam.WithDetail("同步日志信息不能为空")
	}
	if log.SyncType <= 0 {
		return errors.ErrInvalidParam.WithDetail("同步类型无效")
	}
	if log.SyncDirection <= 0 {
		return errors.ErrInvalidParam.WithDetail("同步方向无效")
	}
	if log.BusinessCode == "" {
		return errors.ErrInvalidParam.WithDetail("业务编码不能为空")
	}
	if log.WarehouseCode == "" {
		return errors.ErrInvalidParam.WithDetail("云仓编码不能为空")
	}
	if log.ApiMethod == "" {
		return errors.ErrInvalidParam.WithDetail("API方法不能为空")
	}

	return nil
}
