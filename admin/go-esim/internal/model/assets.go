package model

import (
	"time"

	"github.com/shopspring/decimal"
)

type AssetType string

const (
	AssetTypeCardSupplier   AssetType = "card_supplier"   // 卡供应商
	AssetTypeDeviceSupplier AssetType = "device_supplier" // 设备供应商
	AssetTypeWarehouse      AssetType = "warehouse"       // 云仓
	AssetTypeUser           AssetType = "user"            // 用户
)

type Assets struct {
	ID             uint64          `db:"id"`
	UserID         uint32          `db:"user_id"`
	Balance        decimal.Decimal `db:"balance"`
	CreatedAt      time.Time       `db:"created_at"`
	UpdatedAt      time.Time       `db:"updated_at"`
	LockWithdrawal decimal.Decimal `db:"lock_withdrawal"`
	AssetType      AssetType       `db:"asset_type"`
}
