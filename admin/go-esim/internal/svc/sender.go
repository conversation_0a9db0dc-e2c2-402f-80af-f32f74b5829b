package svc

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/Wenpiner/iot-api/internal/model"
	"github.com/Wenpiner/iot-api/internal/svc/task"
	"github.com/rabbitmq/amqp091-go"
)

func (svc *ServiceContext) SendNewUserData(ctx context.Context, userId uint32, assetType model.AssetType) error {
	// 发送新用户数据
	message := task.NewUserDataBody{
		ID:        userId,
		CreatedAt: time.Now(),
		Type:      assetType,
	}
	msg, err := json.Marshal(message)
	if err != nil {
		return fmt.Erro<PERSON>("序列化新用户数据失败: %w", err)
	}
	return svc.RabbitMQ.SendMessageClose(ctx, task.NewUserExchange, "", true, amqp091.Publishing{
		Body: msg,
	})
}

func (svc *ServiceContext) SendCardImportData(ctx context.Context, body task.ImportBalanceAddBody) error {
	msg, err := json.Marshal(body)
	if err != nil {
		return fmt.Errorf("序列化导入余额增加数据失败: %w", err)
	}
	return svc.RabbitMQ.SendMessageClose(ctx, task.ImportBalanceAddExchange, "", true, amqp091.Publishing{
		Body: msg,
	})
}
