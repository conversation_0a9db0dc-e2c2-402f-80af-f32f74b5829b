package task

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/Wenpiner/iot-api/internal/model"
	"github.com/Wenpiner/iot-api/pkg/db"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rabbitmq/amqp091-go"
	"github.com/wenpiner/rabbitmq-go"
	"github.com/wenpiner/rabbitmq-go/conf"
	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
)

const (
	NewUserDatKeys   = "key:new_user_data"
	NewUserQueueName = "queue:new_user_data"
	NewUserExchange  = "exchange:new_user_data"
	NewUserConsumer  = "consumer:new_user_data"
)

// 如果用新用户创建，则创建对于的数据，如: assets等
type NewUserData struct {
	db *pgxpool.Pool
	mq *rabbitmq.RabbitMQ
}

// 定义一个结构体，用于存储新用户数据
type NewUserDataBody struct {
	ID        uint32    `json:"id"`
	CreatedAt time.Time `json:"created_at"`
	// 新用户类型：（如 "card_supplier"、"device_supplier"、"warehouse"、"user"）
	Type model.AssetType `json:"type"`
}

// NewNewUserData 创建新用户数据
func NewNewUserData(db *pgxpool.Pool, mq *rabbitmq.RabbitMQ) error {
	bean := &NewUserData{db: db, mq: mq}

	// 开始注册消费任务
	return mq.Register(NewUserDatKeys, conf.ConsumerConf{
		Queue:     conf.NewQueue(NewUserQueueName),
		Exchange:  conf.NewFanoutExchange(NewUserExchange),
		RouteKey:  "",
		Name:      NewUserConsumer,
		AutoAck:   true,
		NoLocal:   false,
		NoWait:    false,
		Exclusive: false,
	}, bean)
}

// Receive 消息接收
func (bean *NewUserData) Receive(key string, message amqp091.Delivery) (_ error) {
	// 解析
	var body NewUserDataBody
	err := json.Unmarshal(message.Body, &body)
	if err != nil {
		logx.Errorw("解析新用户数据失败", logx.Field("error", err), logx.Field("level", 1), logx.Field("source", "new-user"), logx.Field("data", string(message.Body)))
		return fmt.Errorf("解析新用户数据失败: %w", err)
	}
	// 创建资产信息
	// 开启事务
	ctx := context.Background()
	tx, err := bean.db.Begin(ctx)
	if err != nil {
		return fmt.Errorf("开启事务失败: %w", err)
	}
	defer tx.Rollback(ctx)

	// 创建资产信息
	err = bean.CreateAssets(ctx, body.ID, body.Type, tx)
	if err != nil {
		return fmt.Errorf("创建资产信息失败: %w", err)
	}
	// 提交事务
	err = tx.Commit(ctx)
	if err != nil {
		return fmt.Errorf("提交事务失败: %w", err)
	}
	return nil
}

// 资产创建，如果错误则返回错误
func (bean *NewUserData) CreateAssets(ctx context.Context, userId uint32, assetType model.AssetType, tx pgx.Tx) error {
	params := pgx.NamedArgs{
		"user_id":    userId,
		"asset_type": assetType,
	}
	// 默认事务已开启
	return db.Insert(ctx, "assets", params, tx.Exec)
}

// Exception 异常处理
func (bean *NewUserData) Exception(key string, err error, message amqp091.Delivery) {
	data := string(message.Body)
	// 当消费异常时，进行特别格式的日志记录,将日志中新增一个error字段
	ctx := logx.ContextWithFields(context.Background(), logx.Field("error", err), logx.Field("level", 1), logx.Field("source", "new-user"), logx.Field("data", data))
	logc.Error(ctx, "新用户消费失败")
}
