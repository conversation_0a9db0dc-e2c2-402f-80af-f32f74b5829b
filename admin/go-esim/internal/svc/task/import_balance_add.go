package task

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/Wenpiner/iot-api/internal/model"
	"github.com/Wenpiner/iot-api/internal/repo"
	"github.com/Wenpiner/iot-api/pkg/lock"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rabbitmq/amqp091-go"
	"github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"
	"github.com/wenpiner/rabbitmq-go"
	"github.com/wenpiner/rabbitmq-go/conf"
	"github.com/zeromicro/go-zero/core/logx"
)

const (
	ImportBalanceAddKeys      = "key:import_balance_add"
	ImportBalanceAddQueueName = "queue:import_balance_add"
	ImportBalanceAddExchange  = "exchange:import_balance_add"
	ImportBalanceAddConsumer  = "consumer:import_balance_add"
)

type ImportBalanceAddBody struct {
	TaskID int     `json:"task_id"`
	Datas  []int64 `json:"datas"`
	Cost   float64 `json:"cost"`
	// 导入类型: card 卡导入, device 设备导入
	Type string `json:"type"`
	// 所属用户ID
	UserID uint32 `json:"user_id"`
}

type ImportBalanceAdd struct {
	db         *pgxpool.Pool
	rd         redis.UniversalClient
	assetsRepo repo.AssetsRepo
}

func NewImportBalanceAdd(db *pgxpool.Pool, rd redis.UniversalClient, mq *rabbitmq.RabbitMQ) error {
	h := ImportBalanceAdd{db: db, rd: rd, assetsRepo: repo.NewAssetsRepo(db, rd)}
	return mq.Register(ImportBalanceAddKeys, conf.ConsumerConf{
		Queue:    conf.NewQueue(ImportBalanceAddQueueName),
		Exchange: conf.NewFanoutExchange(ImportBalanceAddExchange),
		Name:     ImportBalanceAddConsumer,
	}, &h)
}

// Receive 消息接收
func (bean *ImportBalanceAdd) Receive(key string, message amqp091.Delivery) (_ error) {
	// 解析函数
	var body ImportBalanceAddBody
	err := json.Unmarshal(message.Body, &body)
	if err != nil {
		logx.Errorf("解析消息失败: %v", err)
		return err
	}
	var cost decimal.Decimal
	var costType int
	var assetType model.AssetType
	var assetLogType int
	switch body.Type {
	case "card":
		assetType = model.AssetTypeCardSupplier
		assetLogType = 4
		costType = 1
	case "device":
		assetType = model.AssetTypeDeviceSupplier
		assetLogType = 5
		costType = 2
	default:
		logx.Errorf("资产类型错误: %v", body.Type)
		return nil
	}
	cost, err = decimal.NewFromString(fmt.Sprintf("%f", body.Cost))
	if err != nil {
		logx.Errorf("解析成本失败: %v", err)
		return nil
	}
	ctx := context.Background()
	// 获取资产锁
	lBean, err := bean.assetsRepo.GetWriteLock(ctx, body.UserID, assetType)
	if err != nil {
		logx.Errorf("获取资产锁失败: %v", err)
		return err
	}
	// 开始加锁,超时时间10秒，锁TTL 10秒
	err = lBean.Lock(ctx, lock.WithFairAcquireTimeout(10*time.Second), lock.WithFairLockTTL(10*time.Second))
	if err != nil {
		logx.Errorf("加锁失败: %v", err)
		return err
	}
	defer lBean.Unlock(ctx)

	// 读取资产信息
	asset, err := bean.assetsRepo.Get(ctx, body.UserID, assetType, true)
	if err != nil {
		logx.Errorf("读取资产信息失败: %v", err)
		return err
	}
	// 记录当前余额信息
	assetBalance := asset.Balance

	// 开启事务，批量将1千条数据写入到数据库中
	tx, err := bean.db.Begin(ctx)
	if err != nil {
		logx.Errorf("开启事务失败: %v", err)
		return err
	}
	defer tx.Rollback(ctx)

	// 批量将1千条数据写入到数据库中
	var rows [][]interface{}
	// costRows 成本行
	var costRows [][]interface{}

	for _, data := range body.Datas {
		// 创建扩展字段
		extraData := fmt.Sprintf("{\"task_id\":%d,\"data_id\":%d,\"cost\":%s}",
			body.TaskID, data, cost.String())
		now := time.Now()
		// 构建行数据
		row := []interface{}{
			asset.ID,               // asset_id
			assetBalance,           // before_balance
			cost,                   // amount
			assetBalance.Add(cost), // after_balance
			asset.ID,               // parent_id
			assetLogType,           // type
			now,                    // created_at
			extraData,              // extra_data
		}
		rows = append(rows, row)

		// id,related_order_id,related_entity_id,amount,created_at,description,updated_at,is_refunded,transaction_type,asset_id,cost_type
		//default,1,1,1.00,default,1,default,false,2,1,1

		costRow := []interface{}{
			body.TaskID,
			data,
			cost.String(),
			now,
			fmt.Sprintf("导入成本%s", cost.String()),
			now,
			false,
			2,
			asset.ID,
			costType,
		}
		costRows = append(costRows, costRow)

		assetBalance = assetBalance.Add(cost)
	}
	// 执行批量插入
	_, err = tx.CopyFrom(
		ctx,
		pgx.Identifier{"iot", "assets_logs"},
		[]string{"asset_id", "before_balance", "amount", "after_balance",
			"parent_id", "type", "created_at", "extra_data"},
		pgx.CopyFromRows(rows),
	)
	if err != nil {
		logx.Errorf("批量插入资产日志失败: %v", err)
		return err
	}

	// 记录余额增加
	sql := `UPDATE iot.assets SET balance = balance + $1 WHERE id = $2`
	_, err = tx.Exec(ctx, sql, cost, asset.ID)
	if err != nil {
		logx.Errorf("记录余额增加失败: %v", err)
		return err
	}

	// 提交事务
	err = tx.Commit(ctx)
	if err != nil {
		logx.Errorf("提交事务失败: %v", err)
		return err
	}

	// 将成本信息CopyFrom到数据库中
	_, err = bean.db.CopyFrom(
		ctx,
		pgx.Identifier{"iot", "cost_transaction_detail"},
		[]string{"id", "related_order_id", "related_entity_id", "amount", "created_at", "description", "updated_at", "is_refunded", "transaction_type", "asset_id", "cost_type"},
		pgx.CopyFromRows(costRows),
	)
	if err != nil {
		logx.Errorf("批量插入成本信息失败: %v", err)
		return err
	}

	return nil
}

// Exception 异常处理
func (bean *ImportBalanceAdd) Exception(key string, err error, message amqp091.Delivery) {
	data := string(message.Body)
	logx.Errorw("导入余额增加异常", logx.Field("error", err), logx.Field("level", 1), logx.Field("source", "import-balance-add"), logx.Field("data", data))
}
