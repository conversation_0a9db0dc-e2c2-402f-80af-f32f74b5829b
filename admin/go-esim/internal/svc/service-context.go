package svc

import (
	"github.com/Wenpiner/iot-api/internal/config"
	"github.com/Wenpiner/iot-api/internal/middleware"
	"github.com/Wenpiner/iot-api/internal/model"
	"github.com/Wenpiner/iot-api/internal/svc/task"
	"github.com/casbin/casbin"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/redis/go-redis/v9"
	"github.com/wenpiner/rabbitmq-go"
	"github.com/zeromicro/go-zero/rest"
)

type ServiceContext struct {
	Config                 config.Config
	DB                     *pgxpool.Pool
	Redis                  redis.UniversalClient
	Casbin                 *casbin.Enforcer
	Authority              rest.Middleware
	CardChannelRepo        *model.CardChannelRepo
	CardRepo               *model.CardRepo
	TaskRepo               *model.TaskRepo
	RabbitMQ               *rabbitmq.RabbitMQ
	DeviceManufacturerRepo *model.DeviceManufacturerRepo
	WarehouseRepo          *model.WarehouseRepo
	EntryOrderRepo         *model.EntryOrderRepo
	EntryOrderLineRepo     *model.EntryOrderLineRepo
	QimenSyncLogRepo       *model.QimenSyncLogRepo
}

func NewServiceContext(c config.Config, db *pgxpool.Pool, redisClient redis.UniversalClient) *ServiceContext {
	cbn := c.CasbinConf.MustNewCasbinWithOriginalRedisWatcher(c.DatabaseConf.Type,
		c.DatabaseConf.GetDSN(), c.RedisConf)

	// 初始化 RabbitMQ 客户端
	rabbitMQClient := rabbitmq.NewRabbitMQ(c.RabbitMQConf)

	// 初始化新用户任务
	err := task.NewNewUserData(db, rabbitMQClient)
	if err != nil {
		panic(err)
	}

	return &ServiceContext{
		Config:                 c,
		DB:                     db,
		Redis:                  redisClient,
		CardChannelRepo:        model.NewCardChannelRepo(db),
		CardRepo:               model.NewCardRepo(db),
		TaskRepo:               model.NewTaskRepo(db),
		Authority:              middleware.NewAuthorityMiddleware(cbn, redisClient).Handle,
		RabbitMQ:               rabbitMQClient,
		DeviceManufacturerRepo: model.NewDeviceManufacturerRepo(db),
		WarehouseRepo:          model.NewWarehouseRepo(db),
		EntryOrderRepo:         model.NewEntryOrderRepo(db),
		EntryOrderLineRepo:     model.NewEntryOrderLineRepo(db),
		QimenSyncLogRepo:       model.NewQimenSyncLogRepo(db),
	}
}
