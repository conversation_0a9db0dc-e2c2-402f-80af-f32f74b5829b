package svc

import (
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

func (svc *ServiceContext) StartTimeoutTask() {
	// 查询超过30分钟内未更新的任务
	query := `
SELECT id, created_at
FROM iot.task
WHERE status in (2,1) AND created_at < NOW() - INTERVAL '30 minutes'
`
	rows, err := svc.DB.Query(context.Background(), query)
	if err != nil {
		logx.Errorf("查询任务失败: %v", err)
		return
	}
	defer rows.Close()
	for rows.Next() {
		var taskID int
		var createdAt time.Time
		err := rows.Scan(&taskID, &createdAt)
		if err != nil {
			logx.Errorf("扫描任务失败: %v", err)
			continue
		}
		logx.Infof("任务%d超过30分钟未更新，更新状态为失败", taskID)
		update := `
	UPDATE iot.task
	SET status = 4, updated_at = current_timestamp, error_message = '任务超时'
	WHERE id = $1
`
		_, err = svc.DB.Exec(context.Background(), update, taskID)
		if err != nil {
			logx.Errorf("更新任务状态失败: %v", err)
			continue
		}
	}
}
