package svc

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/config"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/redis/go-redis/v9"
)

// 初始化数据库
func InitDB(ctx context.Context, c config.Config) (*pgxpool.Pool, error) {
	connString := c.DatabaseConf.GetDSN()
	db, err := pgxpool.New(ctx, connString)
	if err != nil {
		return nil, err
	}
	err = db.Ping(ctx)
	if err != nil {
		return nil, err
	}
	return db, nil
}

// 初始化redis
func InitRedis(ctx context.Context, c config.Config) (redis.UniversalClient, error) {
	redisClient, err := c.RedisConf.NewUniversalRedis()
	if err != nil {
		return nil, err
	}
	return redisClient, nil
}
