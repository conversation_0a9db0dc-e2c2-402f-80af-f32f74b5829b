package card

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/Wenpiner/iot-api/internal/model"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
	"github.com/Wenpiner/iot-api/pkg/operation"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateChannelLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateChannelLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateChannelLogic {
	return &CreateChannelLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *CreateChannelLogic) CreateChannel(req *types.ChannelCreateReq) (resp *types.ChannelCreateResp, err error) {
	// 卡通道创建
	channel := &model.CardChannel{
		ID:               req.ID,
		Name:             req.Name,
		Region:           req.Region,
		ContactPerson:    req.ContactPerson,
		ContactPhone:     req.ContactPhone,
		RealNameRequired: req.RealNameRequired,
		ChannelType:      req.ChannelType,
		RealNameUrl:      req.RealNameUrl,
		Balance:          req.Balance,
		Operator:         req.Operator,
	}

	// 处理扩展数据
	if req.ExtData != "" {
		var extraData model.JSONB
		if err := json.Unmarshal([]byte(req.ExtData), &extraData); err != nil {
			return nil, err
		}
		channel.ExtraData = extraData
	}

	// 创建通道
	if err := l.svcCtx.CardChannelRepo.Create(l.ctx, channel); err != nil {
		return nil, err
	}

	// 渠道创建成功，发送新用户数据
	if err := l.svcCtx.SendNewUserData(l.ctx, uint32(channel.ID), model.AssetTypeCardSupplier); err != nil {
		logx.Errorf("发送新用户数据失败: %v", err)
		return nil, err
	}

	var targetId int32
	if channel.ID != 0 {
		targetId = int32(channel.ID)
	}
	operation.DBLog(l.ctx, l.svcCtx.DB, "创建卡通道", &targetId, "iot.card_channel", fmt.Sprintf("通道: %s", channel.Name), map[string]interface{}{})

	return &types.ChannelCreateResp{
		BaseMsgResp: types.BaseMsgResp{
			Code: 0,
			Msg:  "创建成功",
		},
	}, nil
}
