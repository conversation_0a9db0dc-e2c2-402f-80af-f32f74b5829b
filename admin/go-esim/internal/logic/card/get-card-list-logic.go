package card

import (
	"context"
	"fmt"
	"time"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
	"github.com/georgysavva/scany/v2/pgxscan"
	"github.com/jackc/pgx/v5"
	"github.com/zeromicro/go-zero/core/logx"
)

type GetCardListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetCardListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetCardListLogic {
	return &GetCardListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *GetCardListLogic) GetCardList(req *types.CardListReq) (resp *types.CardListResp, err error) {
	resp = &types.CardListResp{
		BaseMsgResp: types.BaseMsgResp{
			Code: 0,
			Msg:  "success",
		},
	}

	// 构建查询条件
	whereClause := ""
	args := pgx.NamedArgs{}

	if req.ICCID != nil && *req.ICCID != "" {
		whereClause += " AND c.iccid = @iccid"
		args["iccid"] = *req.ICCID
	}

	if req.Status != nil {
		whereClause += " AND c.status = @status"
		args["status"] = *req.Status
	}

	if len(req.Tags) > 0 {
		whereClause += " AND c.tag && @tags"
		args["tags"] = req.Tags
	}

	if req.Locked != nil {
		whereClause += " AND c.lock_status = @lock_status"
		if *req.Locked {
			args["lock_status"] = 1
		} else {
			args["lock_status"] = 0
		}
	}

	if req.Channel != nil {
		whereClause += " AND c.channel_id = @channel_id"
		args["channel_id"] = *req.Channel
	}

	if len(req.AddedDateRange) == 2 && req.AddedDateRange[0] != nil && req.AddedDateRange[1] != nil {
		whereClause += " AND c.added_date BETWEEN @start_date AND @end_date"
		args["start_date"] = time.Unix(*req.AddedDateRange[0], 0)
		args["end_date"] = time.Unix(*req.AddedDateRange[1], 0)
	}

	// 计算总数
	countQuery := fmt.Sprintf(`
		SELECT COUNT(*) 
		FROM iot.cards c
		WHERE 1=1 %s
	`, whereClause)

	var total int64
	err = l.svcCtx.DB.QueryRow(l.ctx, countQuery, args).Scan(&total)
	if err != nil {
		return nil, err
	}

	// 查询数据
	query := fmt.Sprintf(`
		SELECT 
			c.id, c.iccid, c.msisdn, c.imsi, c.created_at, c.status,
			c.tag, c.added_date, c.lock_status, c.activated_at,
			cc.id as channel_id, cc.name as channel_name, 
			cc.operator as channel_operator, cc.real_name_required as channel_real_name_required
		FROM iot.cards c
		LEFT JOIN iot.card_channels cc ON c.channel_id = cc.id
		WHERE 1=1 %s
		ORDER BY c.created_at DESC
		LIMIT @limit OFFSET @offset
	`, whereClause)

	args["limit"] = req.Page.PageSize
	args["offset"] = (req.Page.Page - 1) * req.Page.PageSize

	type CardRow struct {
		ID                      int64     `db:"id"`
		ICCID                   string    `db:"iccid"`
		MSISDN                  string    `db:"msisdn"`
		IMSI                    string    `db:"imsi"`
		CreatedAt               time.Time `db:"created_at"`
		Status                  int32     `db:"status"`
		Tag                     []string  `db:"tag"`
		AddedDate               time.Time `db:"added_date"`
		LockStatus              int       `db:"lock_status"`
		ActivatedAt             time.Time `db:"activated_at"`
		ChannelID               int64     `db:"channel_id"`
		ChannelName             string    `db:"channel_name"`
		ChannelOperator         string    `db:"channel_operator"`
		ChannelRealNameRequired bool      `db:"channel_real_name_required"`
	}

	var rows []CardRow
	if err := pgxscan.Select(l.ctx, l.svcCtx.DB, &rows, query, args); err != nil {
		return nil, err
	}

	cards := make([]types.CardInfo, 0, len(rows))
	for _, row := range rows {
		card := types.CardInfo{
			ID:          row.ID,
			ICCID:       row.ICCID,
			MSISDN:      row.MSISDN,
			IMSI:        row.IMSI,
			CreatedAt:   row.CreatedAt.Unix(),
			AddedDate:   row.AddedDate.Unix(),
			Tags:        row.Tag,
			Locked:      row.LockStatus == 1,
			ActivatedAt: row.ActivatedAt.Unix(),
			Status:      row.Status,
			Channel: types.CardChannelInfo{
				ID:               row.ChannelID,
				Name:             row.ChannelName,
				Operator:         row.ChannelOperator,
				RealNameRequired: row.ChannelRealNameRequired,
			},
		}
		cards = append(cards, card)
	}

	resp.Data = types.CardListInfo{
		BaseListInfo: types.BaseListInfo{
			Total: uint64(total),
		},
		Data: cards,
	}

	return resp, nil
}
