package card

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/Wenpiner/iot-api/internal/model"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
	"github.com/georgysavva/scany/v2/pgxscan"
	"github.com/jackc/pgx/v5"
	"github.com/zeromicro/go-zero/core/logx"
)

type GetChannelListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetChannelListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetChannelListLogic {
	return &GetChannelListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *GetChannelListLogic) GetChannelList(req *types.ChannelListReq) (resp *types.ChannelListResp, err error) {
	resp = &types.ChannelListResp{
		BaseMsgResp: types.BaseMsgResp{
			Code: 0,
			Msg:  "success",
		},
	}

	// 构建查询条件
	whereClause := ""
	args := pgx.NamedArgs{}

	if req.Name != nil && *req.Name != "" {
		whereClause += " AND name ILIKE @name"
		args["name"] = "%" + *req.Name + "%"
	}

	if req.Region != nil && *req.Region != "" {
		whereClause += " AND region = @region"
		args["region"] = *req.Region
	}

	if req.ChannelType != nil && *req.ChannelType != "" {
		whereClause += " AND channel_type = @channel_type"
		args["channel_type"] = *req.ChannelType
	}

	if req.Operator != nil && *req.Operator != "" {
		whereClause += " AND operator = @operator"
		args["operator"] = *req.Operator
	}

	if req.RealNameRequired != nil {
		whereClause += " AND real_name_required = @real_name_required"
		args["real_name_required"] = *req.RealNameRequired
	}

	// 计算总数
	countQuery := fmt.Sprintf(`
		SELECT COUNT(*) 
		FROM iot.card_channels 
		WHERE 1=1 %s
	`, whereClause)

	var total int64
	err = l.svcCtx.DB.QueryRow(l.ctx, countQuery, args).Scan(&total)
	if err != nil {
		return nil, err
	}

	// 查询数据
	query := fmt.Sprintf(`
		SELECT id, name, region, contact_person, contact_phone, 
			   real_name_required, channel_type, extra_data, real_name_url, 
			   balance, operator, created_at
		FROM iot.card_channels 
		WHERE 1=1 %s
		ORDER BY created_at DESC
		LIMIT @limit OFFSET @offset
	`, whereClause)

	args["limit"] = req.Page.PageSize
	args["offset"] = (req.Page.Page - 1) * req.Page.PageSize

	type ChannelRow struct {
		ID               int64       `db:"id"`
		Name             string      `db:"name"`
		Region           string      `db:"region"`
		ContactPerson    string      `db:"contact_person"`
		ContactPhone     string      `db:"contact_phone"`
		RealNameRequired bool        `db:"real_name_required"`
		ChannelType      string      `db:"channel_type"`
		ExtraData        model.JSONB `db:"extra_data"`
		RealNameUrl      string      `db:"real_name_url"`
		Balance          float64     `db:"balance"`
		Operator         string      `db:"operator"`
		CreatedAt        time.Time   `db:"created_at"`
	}

	var rows []ChannelRow
	if err := pgxscan.Select(l.ctx, l.svcCtx.DB, &rows, query, args); err != nil {
		return nil, err
	}

	channels := make([]types.ChannelListItem, 0, len(rows))
	for _, row := range rows {
		channel := types.ChannelListItem{
			ID:               row.ID,
			Name:             row.Name,
			Region:           row.Region,
			ContactPerson:    row.ContactPerson,
			ContactPhone:     row.ContactPhone,
			RealNameRequired: row.RealNameRequired,
			ChannelType:      row.ChannelType,
			RealNameUrl:      row.RealNameUrl,
			Balance:          row.Balance,
			Operator:         row.Operator,
			CreatedAt:        row.CreatedAt.Unix(),
		}

		// 将JSONB转换为字符串
		if row.ExtraData != nil {
			jsonBytes, err := json.Marshal(row.ExtraData)
			if err != nil {
				return nil, err
			}
			channel.ExtData = string(jsonBytes)
		}

		channels = append(channels, channel)
	}

	resp.Data = types.ChannelListInfo{
		BaseListInfo: types.BaseListInfo{
			Total: uint64(total),
		},
		Data: channels,
	}

	return resp, nil
}
