package qimen

import (
	"context"
	"encoding/json"
	"time"

	"github.com/Wenpiner/iot-api/internal/model"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
	"github.com/Wenpiner/iot-api/pkg/constants"
	"github.com/Wenpiner/iot-api/pkg/errors"
	"github.com/Wenpiner/iot-api/pkg/qimen"
	"github.com/Wenpiner/iot-api/pkg/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type SyncEntryOrderToQimenLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSyncEntryOrderToQimenLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SyncEntryOrderToQimenLogic {
	return &SyncEntryOrderToQimenLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *SyncEntryOrderToQimenLogic) SyncEntryOrderToQimen(req *types.SyncEntryOrderToQimenReq) (resp *types.SyncEntryOrderToQimenResp, err error) {
	// 参数验证
	if req.EntryOrderId <= 0 {
		l.Errorf("入库单ID无效: %d", req.EntryOrderId)
		return &types.SyncEntryOrderToQimenResp{
			BaseMsgResp: types.BaseMsgResp{
				Code: constants.ErrCodeInvalidParam,
				Msg:  "入库单ID无效",
			},
		}, nil
	}

	// 查询入库单信息
	entryOrder, err := l.svcCtx.EntryOrderRepo.GetByID(l.ctx, req.EntryOrderId)
	if err != nil {
		l.Errorf("查询入库单失败: %v", err)
		if warehouseErr := errors.GetWarehouseError(err); warehouseErr != nil {
			return &types.SyncEntryOrderToQimenResp{
				BaseMsgResp: types.BaseMsgResp{
					Code: warehouseErr.Code,
					Msg:  warehouseErr.Message,
				},
			}, nil
		}
		return &types.SyncEntryOrderToQimenResp{
			BaseMsgResp: types.BaseMsgResp{
				Code: constants.ErrCodeDatabaseError,
				Msg:  constants.ErrMsgDatabaseError,
			},
		}, nil
	}

	// 查询云仓配置
	warehouse, err := l.svcCtx.WarehouseRepo.GetByCode(l.ctx, entryOrder.WarehouseCode)
	if err != nil {
		l.Errorf("查询云仓配置失败: %v", err)
		return &types.SyncEntryOrderToQimenResp{
			BaseMsgResp: types.BaseMsgResp{
				Code: constants.ErrCodeWarehouseNotFound,
				Msg:  "云仓配置不存在",
			},
		}, nil
	}

	// 查询入库单明细
	orderLines, err := l.svcCtx.EntryOrderLineRepo.GetLinesByOrderID(l.ctx, req.EntryOrderId)
	if err != nil {
		l.Errorf("查询入库单明细失败: %v", err)
		return &types.SyncEntryOrderToQimenResp{
			BaseMsgResp: types.BaseMsgResp{
				Code: constants.ErrCodeDatabaseError,
				Msg:  "查询入库单明细失败",
			},
		}, nil
	}

	// 构建奇门API请求
	qimenReq := l.buildQimenEntryOrderRequest(entryOrder, orderLines)

	// 构建云仓配置
	warehouseConfig := &qimen.WarehouseConfig{
		ApiEndpoint: warehouse.ApiEndpoint,
		AppKey:      warehouse.AppKey,
		AppSecret:   warehouse.AppSecret,
		CustomerID:  warehouse.CustomerID,
	}

	// 创建同步日志
	syncLog := &model.QimenSyncLog{
		SyncType:      constants.QimenSyncTypeEntry,
		SyncDirection: constants.QimenSyncDirectionPush,
		BusinessID:    entryOrder.ID,
		BusinessCode:  entryOrder.EntryOrderCode,
		WarehouseCode: entryOrder.WarehouseCode,
		ApiMethod:     constants.QimenApiEntryOrderCreate,
		SyncStatus:    constants.QimenSyncStatusFailed, // 默认失败，成功后更新
		RetryCount:    0,
		SyncTime:      time.Now(),
	}

	// 序列化请求数据
	if requestData, marshalErr := json.Marshal(qimenReq); marshalErr == nil {
		syncLog.RequestData = string(requestData)
	}

	// 保存同步日志
	if err := l.svcCtx.QimenSyncLogRepo.Create(l.ctx, syncLog); err != nil {
		l.Errorf("创建同步日志失败: %v", err)
	}

	// 调用奇门API
	qimenClient := qimen.NewClient()
	responseBody, err := qimenClient.CallAPIWithRetry(l.ctx, constants.QimenApiEntryOrderCreate, warehouseConfig, qimenReq, 3)
	if err != nil {
		l.Errorf("调用奇门API失败: %v", err)

		// 更新同步日志状态
		if syncLog.ID > 0 {
			l.svcCtx.QimenSyncLogRepo.UpdateStatus(l.ctx, syncLog.ID, constants.QimenSyncStatusFailed, "", err.Error())
		}

		return &types.SyncEntryOrderToQimenResp{
			BaseMsgResp: types.BaseMsgResp{
				Code: constants.ErrCodeQimenApiError,
				Msg:  "同步到奇门失败: " + err.Error(),
			},
		}, nil
	}

	// 解析响应
	var qimenResp qimen.EntryOrderCreateResponse
	if err := qimenClient.ParseResponse(responseBody, &qimenResp); err != nil {
		l.Errorf("解析奇门API响应失败: %v", err)

		// 更新同步日志状态
		if syncLog.ID > 0 {
			l.svcCtx.QimenSyncLogRepo.UpdateStatus(l.ctx, syncLog.ID, constants.QimenSyncStatusFailed, string(responseBody), err.Error())
		}

		return &types.SyncEntryOrderToQimenResp{
			BaseMsgResp: types.BaseMsgResp{
				Code: constants.ErrCodeQimenApiError,
				Msg:  "解析奇门API响应失败",
			},
		}, nil
	}

	// 更新同步日志状态为成功
	if syncLog.ID > 0 {
		l.svcCtx.QimenSyncLogRepo.UpdateStatus(l.ctx, syncLog.ID, constants.QimenSyncStatusSuccess, string(responseBody), "")
	}

	l.Infof("同步入库单到奇门成功: EntryOrderCode=%s, QimenOrderId=%s", entryOrder.EntryOrderCode, qimenResp.EntryOrderID)

	return &types.SyncEntryOrderToQimenResp{
		BaseMsgResp: types.BaseMsgResp{
			Code: constants.ErrCodeSuccess,
			Msg:  constants.ErrMsgSuccess,
		},
		Data: types.SyncResultData{
			QimenOrderId: qimenResp.EntryOrderID,
			SyncLogId:    syncLog.ID,
			SyncStatus:   constants.QimenSyncStatusSuccess,
			Message:      "同步成功",
		},
	}, nil
}

// buildQimenEntryOrderRequest 构建奇门入库单请求
func (l *SyncEntryOrderToQimenLogic) buildQimenEntryOrderRequest(entryOrder *model.EntryOrder, orderLines []*model.EntryOrderLine) *qimen.EntryOrderCreateRequest {
	req := &qimen.EntryOrderCreateRequest{
		EntryOrderCode:    entryOrder.EntryOrderCode,
		WarehouseCode:     entryOrder.WarehouseCode,
		OwnerCode:         entryOrder.OwnerCode,
		OrderType:         entryOrder.OrderType,
		LogisticsCode:     entryOrder.LogisticsCode,
		LogisticsName:     entryOrder.LogisticsName,
		ExpressCode:       entryOrder.ExpressCode,
		SupplierCode:      entryOrder.SupplierCode,
		SupplierName:      entryOrder.SupplierName,
		PurchaseOrderCode: entryOrder.PurchaseOrderCode,
		Remark:            entryOrder.Remark,
	}

	// 转换时间字段
	if entryOrder.ExpectStartTime != nil {
		req.ExpectStartTime = utils.FormatTimestamp(entryOrder.ExpectStartTime.Unix())
	}
	if entryOrder.ExpectEndTime != nil {
		req.ExpectEndTime = utils.FormatTimestamp(entryOrder.ExpectEndTime.Unix())
	}

	// 转换明细
	req.OrderLines = make([]qimen.EntryOrderLineRequest, 0, len(orderLines))
	for _, line := range orderLines {
		qimenLine := qimen.EntryOrderLineRequest{
			OrderLineNo:   line.OrderLineNo,
			OutBizCode:    line.OutBizCode,
			ItemCode:      line.ItemCode,
			ItemName:      line.ItemName,
			PlanQty:       line.PlanQty,
			PurchasePrice: line.PurchasePrice,
			RetailPrice:   line.RetailPrice,
			InventoryType: line.InventoryType,
			BatchCode:     line.BatchCode,
			ProduceCode:   line.ProduceCode,
			Unit:          line.Unit,
		}

		// 转换时间字段
		if line.ProductDate != nil {
			qimenLine.ProductDate = utils.FormatTimestamp(line.ProductDate.Unix())
		}
		if line.ExpireDate != nil {
			qimenLine.ExpireDate = utils.FormatTimestamp(line.ExpireDate.Unix())
		}

		req.OrderLines = append(req.OrderLines, qimenLine)
	}

	return req
}
