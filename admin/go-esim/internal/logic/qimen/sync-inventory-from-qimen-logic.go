package qimen

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type SyncInventoryFromQimenLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSyncInventoryFromQimenLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SyncInventoryFromQimenLogic {
	return &SyncInventoryFromQimenLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *SyncInventoryFromQimenLogic) SyncInventoryFromQimen(req *types.SyncInventoryFromQimenReq) (resp *types.SyncInventoryFromQimenResp, err error) {
	// todo: add your logic here and delete this line

	return
}
