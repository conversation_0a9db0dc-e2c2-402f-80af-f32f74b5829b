package qimen

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type RetryQimenSyncLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewRetryQimenSyncLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RetryQimenSyncLogic {
	return &RetryQimenSyncLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *RetryQimenSyncLogic) RetryQimenSync(req *types.RetryQimenSyncReq) (resp *types.RetryQimenSyncResp, err error) {
	// todo: add your logic here and delete this line

	return
}
