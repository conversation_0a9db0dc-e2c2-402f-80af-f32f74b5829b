package qimen

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetQimenSyncLogsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetQimenSyncLogsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetQimenSyncLogsLogic {
	return &GetQimenSyncLogsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *GetQimenSyncLogsLogic) GetQimenSyncLogs(req *types.GetQimenSyncLogsReq) (resp *types.GetQimenSyncLogsResp, err error) {
	// todo: add your logic here and delete this line

	return
}
