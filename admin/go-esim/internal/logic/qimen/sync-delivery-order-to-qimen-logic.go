package qimen

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type SyncDeliveryOrderToQimenLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSyncDeliveryOrderToQimenLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SyncDeliveryOrderToQimenLogic {
	return &SyncDeliveryOrderToQimenLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *SyncDeliveryOrderToQimenLogic) SyncDeliveryOrderToQimen(req *types.SyncDeliveryOrderToQimenReq) (resp *types.SyncDeliveryOrderToQimenResp, err error) {
	// todo: add your logic here and delete this line

	return
}
