package delivery

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateDeliveryOrderLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateDeliveryOrderLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateDeliveryOrderLogic {
	return &CreateDeliveryOrderLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *CreateDeliveryOrderLogic) CreateDeliveryOrder(req *types.CreateDeliveryOrderReq) (resp *types.CreateDeliveryOrderResp, err error) {
	// todo: add your logic here and delete this line

	return
}
