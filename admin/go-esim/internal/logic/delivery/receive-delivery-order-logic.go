package delivery

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ReceiveDeliveryOrderLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewReceiveDeliveryOrderLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ReceiveDeliveryOrderLogic {
	return &ReceiveDeliveryOrderLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *ReceiveDeliveryOrderLogic) ReceiveDeliveryOrder(req *types.ReceiveDeliveryOrderReq) (resp *types.ReceiveDeliveryOrderResp, err error) {
	// todo: add your logic here and delete this line

	return
}
