package delivery

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDeliveryOrderListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetDeliveryOrderListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDeliveryOrderListLogic {
	return &GetDeliveryOrderListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *GetDeliveryOrderListLogic) GetDeliveryOrderList(req *types.GetDeliveryOrderListReq) (resp *types.GetDeliveryOrderListResp, err error) {
	// todo: add your logic here and delete this line

	return
}
