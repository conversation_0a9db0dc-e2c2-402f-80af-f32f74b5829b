package delivery

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ShipDeliveryOrderLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewShipDeliveryOrderLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ShipDeliveryOrderLogic {
	return &ShipDeliveryOrderLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *ShipDeliveryOrderLogic) ShipDeliveryOrder(req *types.ShipDeliveryOrderReq) (resp *types.ShipDeliveryOrderResp, err error) {
	// todo: add your logic here and delete this line

	return
}
