package delivery

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ConfirmDeliveryOrderLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewConfirmDeliveryOrderLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ConfirmDeliveryOrderLogic {
	return &ConfirmDeliveryOrderLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *ConfirmDeliveryOrderLogic) ConfirmDeliveryOrder(req *types.ConfirmDeliveryOrderReq) (resp *types.ConfirmDeliveryOrderResp, err error) {
	// todo: add your logic here and delete this line

	return
}
