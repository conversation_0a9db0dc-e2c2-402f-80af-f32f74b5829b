package delivery

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDeliveryOrderDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetDeliveryOrderDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDeliveryOrderDetailLogic {
	return &GetDeliveryOrderDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *GetDeliveryOrderDetailLogic) GetDeliveryOrderDetail(req *types.GetDeliveryOrderDetailReq) (resp *types.GetDeliveryOrderDetailResp, err error) {
	// todo: add your logic here and delete this line

	return
}
