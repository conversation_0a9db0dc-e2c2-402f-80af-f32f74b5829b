package task

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Wenpiner/iot-api/internal/model"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
	"github.com/georgysavva/scany/v2/pgxscan"
	"github.com/jackc/pgx/v5"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetTaskListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetTaskListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetTaskListLogic {
	return &GetTaskListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *GetTaskListLogic) GetTaskList(req *types.TaskListReq) (resp *types.TaskListResp, err error) {
	resp = &types.TaskListResp{
		BaseMsgResp: types.BaseMsgResp{
			Code: 0,
			Msg:  "success",
		},
	}

	// 构建查询条件
	whereClause := ""
	args := pgx.NamedArgs{}

	if req.Type != nil && *req.Type != 0 {
		whereClause += " AND type = @type"
		args["type"] = *req.Type
	}

	if req.Status != nil && *req.Status != 0 {
		whereClause += " AND status = @status"
		args["status"] = *req.Status
	}

	// 计算总数
	countQuery := fmt.Sprintf(`
		SELECT COUNT(*) 
		FROM iot.task 
		WHERE 1=1 %s
	`, whereClause)

	var total int64
	err = l.svcCtx.DB.QueryRow(l.ctx, countQuery, args).Scan(&total)
	if err != nil {
		logx.Errorf("get task list count error: %v", err)
		return nil, err
	}

	// 查询数据
	query := fmt.Sprintf(`
		SELECT t.id, t.type, u.username as operator_id, t.status, t.success_records, 
		       t.total_records, t.failed_records, t.error_message, t.created_at, 
		       t.updated_at, t.ext_data
		FROM iot.task t
		LEFT JOIN public.sys_users u ON t.operator_id = u.id
		WHERE 1=1 %s
		ORDER BY t.created_at DESC
		LIMIT @limit OFFSET @offset
	`, whereClause)

	args["limit"] = req.Page.PageSize
	args["offset"] = (req.Page.Page - 1) * req.Page.PageSize

	type TaskRow struct {
		ID             int64          `db:"id"`
		Type           int32          `db:"type"`
		OperatorId     string         `db:"operator_id"`
		Status         int32          `db:"status"`
		SuccessRecords int64          `db:"success_records"`
		TotalRecords   int64          `db:"total_records"`
		FailedRecords  int64          `db:"failed_records"`
		ErrorMessage   sql.NullString `db:"error_message"`
		CreatedAt      time.Time      `db:"created_at"`
		UpdatedAt      time.Time      `db:"updated_at"`
		ExtData        model.JSONB    `db:"ext_data"`
	}

	var taskRows []TaskRow
	err = pgxscan.Select(l.ctx, l.svcCtx.DB, &taskRows, query, args)
	if err != nil {
		logx.Errorf("get task list error: %v", err)
		return nil, err
	}

	resp.Data.Total = uint64(total)
	resp.Data.Data = make([]types.TaskListItem, 0)
	for _, task := range taskRows {
		resp.Data.Data = append(resp.Data.Data, types.TaskListItem{
			ID:             task.ID,
			Type:           task.Type,
			OperatorId:     task.OperatorId,
			Status:         task.Status,
			SuccessRecords: task.SuccessRecords,
			TotalRecords:   task.TotalRecords,
			FailedRecords:  task.FailedRecords,
			ErrorMessage:   task.ErrorMessage.String,
			CreatedAt:      task.CreatedAt.Unix(),
			UpdatedAt:      task.UpdatedAt.Unix(),
			ExtData:        task.ExtData.String(),
		})
	}
	return resp, nil
}
