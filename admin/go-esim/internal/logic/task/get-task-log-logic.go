package task

import (
	"context"
	"fmt"
	"time"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
	"github.com/georgysavva/scany/v2/pgxscan"
	"github.com/jackc/pgx/v5"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetTaskLogLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetTaskLogLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetTaskLogLogic {
	return &GetTaskLogLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *GetTaskLogLogic) GetTaskLog(req *types.TaskLogReq) (resp *types.TaskLogResp, err error) {
	resp = &types.TaskLogResp{
		BaseMsgResp: types.BaseMsgResp{
			Code: 0,
			Msg:  "success",
		},
	}

	// 构建查询条件
	whereClause := "WHERE task_id = @task_id"
	args := pgx.NamedArgs{}

	// 计算总数
	countQuery := `
		SELECT COUNT(*) 
		FROM iot.task_log
	`

	args["task_id"] = req.TaskID
	if req.Status != nil && *req.Status != 0 {
		whereClause += " AND status = @status"
		args["status"] = *req.Status
	}

	var total int64
	err = l.svcCtx.DB.QueryRow(l.ctx, fmt.Sprintf("%s%s", countQuery, whereClause), args).Scan(&total)
	if err != nil {
		return nil, err
	}

	// 查询数据
	query := `
		SELECT id, task_id, record_id, record_text, status, created_at, error_text
		FROM iot.task_log
	`
	whereClause += `
			ORDER BY created_at DESC
		LIMIT @limit OFFSET @offset
	`
	args["limit"] = req.Page.PageSize
	args["offset"] = (req.Page.Page - 1) * req.Page.PageSize

	type TaskLogRow struct {
		ID         int64     `db:"id"`
		TaskID     int64     `db:"task_id"`
		RecordID   int32     `db:"record_id"`
		RecordText string    `db:"record_text"`
		Status     int32     `db:"status"`
		CreatedAt  time.Time `db:"created_at"`
		ErrorText  string    `db:"error_text"`
	}

	var taskLogRows []TaskLogRow
	err = pgxscan.Select(l.ctx, l.svcCtx.DB, &taskLogRows, fmt.Sprintf("%s%s", query, whereClause), args)
	if err != nil {
		logx.Errorf("获取任务日志数据失败:%v", err)
		return nil, err
	}

	resp.Data.Total = uint64(total)
	resp.Data.Data = make([]types.TaskLogInfo, 0)
	for _, taskLog := range taskLogRows {
		resp.Data.Data = append(resp.Data.Data, types.TaskLogInfo{
			ID:         taskLog.ID,
			TaskID:     taskLog.TaskID,
			RecordId:   taskLog.RecordID,
			RecordText: taskLog.RecordText,
			Status:     taskLog.Status,
			CreatedAt:  taskLog.CreatedAt.Unix(),
			ErrorText:  taskLog.ErrorText,
		})
	}
	return resp, nil
}
