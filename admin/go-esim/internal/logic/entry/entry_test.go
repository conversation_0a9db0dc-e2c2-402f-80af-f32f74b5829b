package entry

import (
	"testing"

	"github.com/Wenpiner/iot-api/internal/types"
	"github.com/Wenpiner/iot-api/pkg/constants"
)

// TestCreateEntryOrder_ValidateRequest 测试创建入库单的参数验证
func TestCreateEntryOrder_ValidateRequest(t *testing.T) {
	logic := &CreateEntryOrderLogic{}

	tests := []struct {
		name    string
		req     *types.CreateEntryOrderReq
		wantErr bool
	}{
		{
			name:    "空请求",
			req:     nil,
			wantErr: true,
		},
		{
			name: "云仓编码为空",
			req: &types.CreateEntryOrderReq{
				WarehouseCode: "",
			},
			wantErr: true,
		},
		{
			name: "供应商编码为空",
			req: &types.CreateEntryOrderReq{
				WarehouseCode: "WH001",
				SupplierCode:  "",
			},
			wantErr: true,
		},
		{
			name: "供应商名称为空",
			req: &types.CreateEntryOrderReq{
				WarehouseCode: "WH001",
				SupplierCode:  "SUP001",
				SupplierName:  "",
			},
			wantErr: true,
		},
		{
			name: "入库明细为空",
			req: &types.CreateEntryOrderReq{
				WarehouseCode: "WH001",
				SupplierCode:  "SUP001",
				SupplierName:  "供应商A",
				OrderLines:    []types.CreateEntryOrderLineReq{},
			},
			wantErr: true,
		},
		{
			name: "明细商品编码无效",
			req: &types.CreateEntryOrderReq{
				WarehouseCode: "WH001",
				SupplierCode:  "SUP001",
				SupplierName:  "供应商A",
				OrderLines: []types.CreateEntryOrderLineReq{
					{
						ItemCode: "",
						ItemName: "商品A",
						PlanQty:  10,
					},
				},
			},
			wantErr: true,
		},
		{
			name: "明细商品名称为空",
			req: &types.CreateEntryOrderReq{
				WarehouseCode: "WH001",
				SupplierCode:  "SUP001",
				SupplierName:  "供应商A",
				OrderLines: []types.CreateEntryOrderLineReq{
					{
						ItemCode: "ITEM001",
						ItemName: "",
						PlanQty:  10,
					},
				},
			},
			wantErr: true,
		},
		{
			name: "明细计划数量无效",
			req: &types.CreateEntryOrderReq{
				WarehouseCode: "WH001",
				SupplierCode:  "SUP001",
				SupplierName:  "供应商A",
				OrderLines: []types.CreateEntryOrderLineReq{
					{
						ItemCode: "ITEM001",
						ItemName: "商品A",
						PlanQty:  0,
					},
				},
			},
			wantErr: true,
		},
		{
			name: "有效请求",
			req: &types.CreateEntryOrderReq{
				WarehouseCode:     "WH001",
				OrderType:         constants.OrderTypeCGRK,
				PurchaseOrderCode: "PO20240101001",
				SupplierCode:      "SUP001",
				SupplierName:      "供应商A",
				OrderLines: []types.CreateEntryOrderLineReq{
					{
						ItemCode:      "ITEM001",
						ItemName:      "商品A",
						PlanQty:       10,
						PurchasePrice: 100.0,
						RetailPrice:   150.0,
					},
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := logic.validateCreateRequest(tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("validateCreateRequest() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// TestConfirmEntryOrder_ValidateRequest 测试确认入库的参数验证
func TestConfirmEntryOrder_ValidateRequest(t *testing.T) {
	logic := &ConfirmEntryOrderLogic{}

	tests := []struct {
		name    string
		req     *types.ConfirmEntryOrderReq
		wantErr bool
	}{
		{
			name:    "空请求",
			req:     nil,
			wantErr: true,
		},
		{
			name: "ID无效",
			req: &types.ConfirmEntryOrderReq{
				Id: 0,
			},
			wantErr: true,
		},
		{
			name: "明细ID无效",
			req: &types.ConfirmEntryOrderReq{
				Id: 1,
				OrderLines: []types.ConfirmEntryOrderLineReq{
					{
						Id:        0,
						ActualQty: 10,
					},
				},
			},
			wantErr: true,
		},
		{
			name: "明细实际数量无效",
			req: &types.ConfirmEntryOrderReq{
				Id: 1,
				OrderLines: []types.ConfirmEntryOrderLineReq{
					{
						Id:        1,
						ActualQty: -1,
					},
				},
			},
			wantErr: true,
		},
		{
			name: "有效请求",
			req: &types.ConfirmEntryOrderReq{
				Id:                  1,
				ActualArrivalTime:   "2024-01-01 10:30:00",
				OperatorName:        "操作员A",
				Remark:              "确认入库",
				OrderLines: []types.ConfirmEntryOrderLineReq{
					{
						Id:            1,
						ActualQty:     10,
						ShelfLocation: "A-01-01",
						DeviceStatus:  constants.DeviceStatusNormal,
						Remark:        "入库正常",
					},
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := logic.validateConfirmRequest(tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("validateConfirmRequest() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// TestCancelEntryOrder_ValidateRequest 测试取消入库单的参数验证
func TestCancelEntryOrder_ValidateRequest(t *testing.T) {
	logic := &CancelEntryOrderLogic{}

	tests := []struct {
		name    string
		req     *types.CancelEntryOrderReq
		wantErr bool
	}{
		{
			name:    "空请求",
			req:     nil,
			wantErr: true,
		},
		{
			name: "ID无效",
			req: &types.CancelEntryOrderReq{
				Id: 0,
			},
			wantErr: true,
		},
		{
			name: "取消原因为空",
			req: &types.CancelEntryOrderReq{
				Id:           1,
				CancelReason: "",
			},
			wantErr: true,
		},
		{
			name: "有效请求",
			req: &types.CancelEntryOrderReq{
				Id:           1,
				CancelReason: "供应商取消发货",
				OperatorName: "操作员A",
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := logic.validateCancelRequest(tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("validateCancelRequest() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// TestBatchCreateEntryLines_ValidateRequest 测试批量创建入库明细的参数验证
func TestBatchCreateEntryLines_ValidateRequest(t *testing.T) {
	logic := &BatchCreateEntryLinesLogic{}

	tests := []struct {
		name    string
		req     *types.BatchCreateEntryLinesReq
		wantErr bool
	}{
		{
			name:    "空请求",
			req:     nil,
			wantErr: true,
		},
		{
			name: "入库单ID无效",
			req: &types.BatchCreateEntryLinesReq{
				EntryOrderId: 0,
			},
			wantErr: true,
		},
		{
			name: "明细为空",
			req: &types.BatchCreateEntryLinesReq{
				EntryOrderId: 1,
				OrderLines:   []types.CreateEntryOrderLineReq{},
			},
			wantErr: true,
		},
		{
			name: "明细数量超限",
			req: &types.BatchCreateEntryLinesReq{
				EntryOrderId: 1,
				OrderLines:   make([]types.CreateEntryOrderLineReq, 101),
			},
			wantErr: true,
		},
		{
			name: "有效请求",
			req: &types.BatchCreateEntryLinesReq{
				EntryOrderId: 1,
				OrderLines: []types.CreateEntryOrderLineReq{
					{
						ItemCode:      "ITEM001",
						ItemName:      "商品A",
						PlanQty:       10,
						PurchasePrice: 100.0,
						RetailPrice:   150.0,
					},
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := logic.validateBatchCreateRequest(tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("validateBatchCreateRequest() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// TestBatchCreateEntryLines_ValidateSingleLine 测试单个明细验证
func TestBatchCreateEntryLines_ValidateSingleLine(t *testing.T) {
	logic := &BatchCreateEntryLinesLogic{}

	tests := []struct {
		name      string
		lineReq   types.CreateEntryOrderLineReq
		lineIndex int
		wantErr   bool
	}{
		{
			name: "商品编码为空",
			lineReq: types.CreateEntryOrderLineReq{
				ItemCode: "",
				ItemName: "商品A",
				PlanQty:  10,
			},
			lineIndex: 1,
			wantErr:   true,
		},
		{
			name: "商品名称为空",
			lineReq: types.CreateEntryOrderLineReq{
				ItemCode: "ITEM001",
				ItemName: "",
				PlanQty:  10,
			},
			lineIndex: 1,
			wantErr:   true,
		},
		{
			name: "计划数量无效",
			lineReq: types.CreateEntryOrderLineReq{
				ItemCode: "ITEM001",
				ItemName: "商品A",
				PlanQty:  0,
			},
			lineIndex: 1,
			wantErr:   true,
		},
		{
			name: "有效明细",
			lineReq: types.CreateEntryOrderLineReq{
				ItemCode: "ITEM001",
				ItemName: "商品A",
				PlanQty:  10,
			},
			lineIndex: 1,
			wantErr:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := logic.validateSingleLine(tt.lineReq, tt.lineIndex)
			if (err != nil) != tt.wantErr {
				t.Errorf("validateSingleLine() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
