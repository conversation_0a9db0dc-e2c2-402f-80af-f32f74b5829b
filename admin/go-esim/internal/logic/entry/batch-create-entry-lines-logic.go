package entry

import (
	"context"
	"fmt"
	"time"

	"github.com/Wenpiner/iot-api/internal/model"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
	"github.com/Wenpiner/iot-api/pkg/constants"
	"github.com/Wenpiner/iot-api/pkg/errors"
	"github.com/Wenpiner/iot-api/pkg/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type BatchCreateEntryLinesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewBatchCreateEntryLinesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BatchCreateEntryLinesLogic {
	return &BatchCreateEntryLinesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *BatchCreateEntryLinesLogic) BatchCreateEntryLines(req *types.BatchCreateEntryLinesReq) (resp *types.BatchCreateEntryLinesResp, err error) {
	// 参数验证
	if err := l.validateBatchCreateRequest(req); err != nil {
		l.Errorf("批量创建入库明细参数验证失败: %v", err)
		return &types.BatchCreateEntryLinesResp{
			BaseMsgResp: types.BaseMsgResp{
				Code: constants.ErrCodeInvalidParam,
				Msg:  err.Error(),
			},
		}, nil
	}

	// 查询入库单信息
	entryOrder, err := l.svcCtx.EntryOrderRepo.GetByID(l.ctx, req.EntryOrderId)
	if err != nil {
		l.Errorf("查询入库单失败: %v", err)
		if warehouseErr := errors.GetWarehouseError(err); warehouseErr != nil {
			return &types.BatchCreateEntryLinesResp{
				BaseMsgResp: types.BaseMsgResp{
					Code: warehouseErr.Code,
					Msg:  warehouseErr.Message,
				},
			}, nil
		}
		return &types.BatchCreateEntryLinesResp{
			BaseMsgResp: types.BaseMsgResp{
				Code: constants.ErrCodeDatabaseError,
				Msg:  constants.ErrMsgDatabaseError,
			},
		}, nil
	}

	// 检查入库单状态
	if entryOrder.OrderStatus != constants.EntryOrderStatusPending &&
		entryOrder.OrderStatus != constants.EntryOrderStatusPartial {
		l.Errorf("入库单状态不允许添加明细: %d", entryOrder.OrderStatus)
		return &types.BatchCreateEntryLinesResp{
			BaseMsgResp: types.BaseMsgResp{
				Code: constants.ErrCodeOrderStatusInvalid,
				Msg:  "入库单状态不允许添加明细",
			},
		}, nil
	}

	// 构建入库明细列表
	lines := make([]*model.EntryOrderLine, 0, len(req.OrderLines))
	failDetails := make([]string, 0)
	successCount := int32(0)
	failCount := int32(0)

	for i, lineReq := range req.OrderLines {
		// 验证单个明细
		if err := l.validateSingleLine(lineReq, i+1); err != nil {
			failCount++
			failDetails = append(failDetails, err.Error())
			continue
		}

		line := &model.EntryOrderLine{
			EntryOrderID:   req.EntryOrderId,
			EntryOrderCode: entryOrder.EntryOrderCode,
			OwnerCode:      constants.DefaultOwnerCode,
			ItemCode:       lineReq.ItemCode,
			ItemName:       lineReq.ItemName,
			PlanQty:        lineReq.PlanQty,
			PurchasePrice:  lineReq.PurchasePrice,
			RetailPrice:    lineReq.RetailPrice,
			BatchCode:      lineReq.BatchCode,
			ProduceCode:    lineReq.ProduceCode,
			BoxNumber:      lineReq.BoxNumber,
			PalletNumber:   lineReq.PalletNumber,
			Remark:         lineReq.Remark,
			InventoryType:  constants.InventoryTypeZP,
			Unit:           constants.DefaultUnit,
			DeviceStatus:   constants.DeviceStatusNormal,
		}

		// 解析日期字段
		if lineReq.ProductDate != "" {
			if productDate, parseErr := utils.ParseTimestamp(lineReq.ProductDate); parseErr == nil {
				t := time.Unix(productDate, 0)
				line.ProductDate = &t
			}
		}
		if lineReq.ExpireDate != "" {
			if expireDate, parseErr := utils.ParseTimestamp(lineReq.ExpireDate); parseErr == nil {
				t := time.Unix(expireDate, 0)
				line.ExpireDate = &t
			}
		}

		// 设置SN编码
		if len(lineReq.SnCodes) > 0 {
			line.SnCodes = model.JSONArray(lineReq.SnCodes)
		}

		lines = append(lines, line)
		successCount++
	}

	// 批量创建明细
	if len(lines) > 0 {
		if err := l.svcCtx.EntryOrderLineRepo.CreateLines(l.ctx, lines); err != nil {
			l.Errorf("批量创建入库明细失败: %v", err)
			return &types.BatchCreateEntryLinesResp{
				BaseMsgResp: types.BaseMsgResp{
					Code: constants.ErrCodeDatabaseError,
					Msg:  "批量创建入库明细失败",
				},
			}, nil
		}

		// 更新入库单统计信息
		if err := l.svcCtx.EntryOrderRepo.UpdateQuantities(l.ctx, req.EntryOrderId); err != nil {
			l.Errorf("更新入库单统计信息失败: %v", err)
		}
	}

	l.Infof("批量创建入库明细完成: EntryOrderId=%d, Success=%d, Fail=%d", req.EntryOrderId, successCount, failCount)

	return &types.BatchCreateEntryLinesResp{
		BaseMsgResp: types.BaseMsgResp{
			Code: constants.ErrCodeSuccess,
			Msg:  constants.ErrMsgSuccess,
		},
		Data: types.BatchCreateEntryLinesData{
			SuccessCount: successCount,
			FailCount:    failCount,
			FailDetails:  failDetails,
		},
	}, nil
}

// validateBatchCreateRequest 验证批量创建请求参数
func (l *BatchCreateEntryLinesLogic) validateBatchCreateRequest(req *types.BatchCreateEntryLinesReq) error {
	if req == nil {
		return errors.ErrInvalidParam.WithDetail("请求参数不能为空")
	}
	if req.EntryOrderId <= 0 {
		return errors.ErrInvalidParam.WithDetail("入库单ID无效")
	}
	if len(req.OrderLines) == 0 {
		return errors.ErrInvalidParam.WithDetail("入库明细不能为空")
	}
	if len(req.OrderLines) > 100 {
		return errors.ErrInvalidParam.WithDetail("单次批量创建明细数量不能超过100条")
	}

	return nil
}

// validateSingleLine 验证单个明细参数
func (l *BatchCreateEntryLinesLogic) validateSingleLine(lineReq types.CreateEntryOrderLineReq, lineIndex int) error {
	if !utils.ValidateItemCode(lineReq.ItemCode) {
		return errors.ErrInvalidParam.WithDetail(fmt.Sprintf("第%d行商品编码格式无效", lineIndex))
	}
	if lineReq.ItemName == "" {
		return errors.ErrInvalidParam.WithDetail(fmt.Sprintf("第%d行商品名称不能为空", lineIndex))
	}
	if lineReq.PlanQty <= 0 {
		return errors.ErrInvalidParam.WithDetail(fmt.Sprintf("第%d行计划数量必须大于0", lineIndex))
	}

	return nil
}
