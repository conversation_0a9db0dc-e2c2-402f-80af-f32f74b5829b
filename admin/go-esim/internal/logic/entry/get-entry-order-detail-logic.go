package entry

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
	"github.com/Wenpiner/iot-api/pkg/constants"
	"github.com/Wenpiner/iot-api/pkg/errors"
	"github.com/Wenpiner/iot-api/pkg/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetEntryOrderDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetEntryOrderDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetEntryOrderDetailLogic {
	return &GetEntryOrderDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *GetEntryOrderDetailLogic) GetEntryOrderDetail(req *types.GetEntryOrderDetailReq) (resp *types.GetEntryOrderDetailResp, err error) {
	// 参数验证
	if req.Id <= 0 {
		l.Errorf("入库单ID无效: %d", req.Id)
		return &types.GetEntryOrderDetailResp{
			BaseMsgResp: types.BaseMsgResp{
				Code: constants.ErrCodeInvalidParam,
				Msg:  "入库单ID无效",
			},
		}, nil
	}

	// 查询入库单基本信息
	entryOrder, err := l.svcCtx.EntryOrderRepo.GetByID(l.ctx, req.Id)
	if err != nil {
		l.Errorf("查询入库单详情失败: %v", err)

		// 处理业务错误
		if warehouseErr := errors.GetWarehouseError(err); warehouseErr != nil {
			return &types.GetEntryOrderDetailResp{
				BaseMsgResp: types.BaseMsgResp{
					Code: warehouseErr.Code,
					Msg:  warehouseErr.Message,
				},
			}, nil
		}

		// 系统错误
		return &types.GetEntryOrderDetailResp{
			BaseMsgResp: types.BaseMsgResp{
				Code: constants.ErrCodeDatabaseError,
				Msg:  constants.ErrMsgDatabaseError,
			},
		}, nil
	}

	// 查询入库单明细
	orderLines, err := l.svcCtx.EntryOrderLineRepo.GetLinesByOrderID(l.ctx, req.Id)
	if err != nil {
		l.Errorf("查询入库单明细失败: %v", err)
		return &types.GetEntryOrderDetailResp{
			BaseMsgResp: types.BaseMsgResp{
				Code: constants.ErrCodeDatabaseError,
				Msg:  "查询入库单明细失败",
			},
		}, nil
	}

	// 转换入库单数据格式
	entryOrderData := types.EntryOrder{
		Id:                entryOrder.ID,
		EntryOrderCode:    entryOrder.EntryOrderCode,
		WarehouseCode:     entryOrder.WarehouseCode,
		WarehouseName:     entryOrder.WarehouseName,
		OwnerCode:         entryOrder.OwnerCode,
		OrderType:         entryOrder.OrderType,
		PurchaseOrderCode: entryOrder.PurchaseOrderCode,
		LogisticsCode:     entryOrder.LogisticsCode,
		LogisticsName:     entryOrder.LogisticsName,
		ExpressCode:       entryOrder.ExpressCode,
		SupplierCode:      entryOrder.SupplierCode,
		SupplierName:      entryOrder.SupplierName,
		OperatorCode:      entryOrder.OperatorCode,
		OperatorName:      entryOrder.OperatorName,
		TotalOrderLines:   entryOrder.TotalOrderLines,
		ExpectedQuantity:  entryOrder.ExpectedQuantity,
		ActualQuantity:    entryOrder.ActualQuantity,
		OrderStatus:       entryOrder.OrderStatus,
		Remark:            entryOrder.Remark,
		QimenEntryOrderID: entryOrder.QimenEntryOrderID,
		ApiSyncStatus:     entryOrder.ApiSyncStatus,
		ApiErrorMessage:   entryOrder.ApiErrorMessage,
		CreatedAt:         entryOrder.CreatedAt.Unix(),
		UpdatedAt:         entryOrder.UpdatedAt.Unix(),
	}

	// 转换时间字段
	if entryOrder.ExpectStartTime != nil {
		entryOrderData.ExpectStartTime = utils.FormatTimestamp(entryOrder.ExpectStartTime.Unix())
	}
	if entryOrder.ExpectEndTime != nil {
		entryOrderData.ExpectEndTime = utils.FormatTimestamp(entryOrder.ExpectEndTime.Unix())
	}
	if entryOrder.ActualArrivalTime != nil {
		entryOrderData.ActualArrivalTime = utils.FormatTimestamp(entryOrder.ActualArrivalTime.Unix())
	}
	if entryOrder.OperateTime != nil {
		entryOrderData.OperateTime = utils.FormatTimestamp(entryOrder.OperateTime.Unix())
	}
	if entryOrder.ApiSyncTime != nil {
		entryOrderData.ApiSyncTime = utils.FormatTimestamp(entryOrder.ApiSyncTime.Unix())
	}

	// 转换明细数据格式
	orderLinesData := make([]types.EntryOrderLine, 0, len(orderLines))
	for _, line := range orderLines {
		lineData := types.EntryOrderLine{
			Id:             line.ID,
			EntryOrderID:   line.EntryOrderID,
			EntryOrderCode: line.EntryOrderCode,
			OrderLineNo:    line.OrderLineNo,
			OutBizCode:     line.OutBizCode,
			OwnerCode:      line.OwnerCode,
			ItemCode:       line.ItemCode,
			ItemID:         line.ItemID,
			ItemName:       line.ItemName,
			SkuProperty:    line.SkuProperty,
			PlanQty:        line.PlanQty,
			ActualQty:      line.ActualQty,
			PurchasePrice:  line.PurchasePrice,
			RetailPrice:    line.RetailPrice,
			InventoryType:  line.InventoryType,
			BatchCode:      line.BatchCode,
			ProduceCode:    line.ProduceCode,
			BoxNumber:      line.BoxNumber,
			PalletNumber:   line.PalletNumber,
			Unit:           line.Unit,
			SnCodes:        []string(line.SnCodes),
			ShelfLocation:  line.ShelfLocation,
			DeviceStatus:   line.DeviceStatus,
			Remark:         line.Remark,
			CreatedAt:      line.CreatedAt.Unix(),
		}

		// 转换时间字段
		if line.ProductDate != nil {
			lineData.ProductDate = utils.FormatTimestamp(line.ProductDate.Unix())
		}
		if line.ExpireDate != nil {
			lineData.ExpireDate = utils.FormatTimestamp(line.ExpireDate.Unix())
		}
		if line.InboundTime != nil {
			lineData.InboundTime = utils.FormatTimestamp(line.InboundTime.Unix())
		}

		orderLinesData = append(orderLinesData, lineData)
	}

	// 设置明细列表
	entryOrderData.OrderLines = orderLinesData

	return &types.GetEntryOrderDetailResp{
		BaseMsgResp: types.BaseMsgResp{
			Code: constants.ErrCodeSuccess,
			Msg:  constants.ErrMsgSuccess,
		},
		Data: entryOrderData,
	}, nil
}
