package entry

import (
	"context"
	"fmt"
	"time"

	"github.com/Wenpiner/iot-api/internal/model"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
	"github.com/Wenpiner/iot-api/pkg/constants"
	"github.com/Wenpiner/iot-api/pkg/errors"
	"github.com/Wenpiner/iot-api/pkg/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateEntryOrderLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateEntryOrderLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateEntryOrderLogic {
	return &CreateEntryOrderLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *CreateEntryOrderLogic) CreateEntryOrder(req *types.CreateEntryOrderReq) (resp *types.CreateEntryOrderResp, err error) {
	// 参数验证
	if err := l.validateCreateRequest(req); err != nil {
		l.Errorf("创建入库单参数验证失败: %v", err)
		return &types.CreateEntryOrderResp{
			BaseMsgResp: types.BaseMsgResp{
				Code: constants.ErrCodeInvalidParam,
				Msg:  err.Error(),
			},
		}, nil
	}

	// 验证云仓是否存在
	warehouse, err := l.svcCtx.WarehouseRepo.GetByCode(l.ctx, req.WarehouseCode)
	if err != nil {
		l.Errorf("查询云仓失败: %v", err)
		if warehouseErr := errors.GetWarehouseError(err); warehouseErr != nil {
			return &types.CreateEntryOrderResp{
				BaseMsgResp: types.BaseMsgResp{
					Code: warehouseErr.Code,
					Msg:  warehouseErr.Message,
				},
			}, nil
		}
		return &types.CreateEntryOrderResp{
			BaseMsgResp: types.BaseMsgResp{
				Code: constants.ErrCodeDatabaseError,
				Msg:  constants.ErrMsgDatabaseError,
			},
		}, nil
	}

	// 构建入库单模型
	entryOrder := &model.EntryOrder{
		WarehouseCode:     req.WarehouseCode,
		WarehouseName:     warehouse.WarehouseName,
		OwnerCode:         constants.DefaultOwnerCode,
		OrderType:         req.OrderType,
		OrderCreateTime:   &time.Time{},
		PurchaseOrderCode: req.PurchaseOrderCode,
		LogisticsCode:     req.LogisticsCode,
		LogisticsName:     req.LogisticsName,
		ExpressCode:       req.ExpressCode,
		SupplierCode:      req.SupplierCode,
		SupplierName:      req.SupplierName,
		OperatorName:      req.OperatorName,
		Remark:            req.Remark,
		OrderStatus:       constants.EntryOrderStatusPending,
		ApiSyncStatus:     constants.ApiSyncStatusNotSynced,
	}

	// 解析时间字段
	if req.ExpectStartTime != "" {
		if startTime, parseErr := utils.ParseTimestamp(req.ExpectStartTime); parseErr == nil {
			t := time.Unix(startTime, 0)
			entryOrder.ExpectStartTime = &t
		}
	}
	if req.ExpectEndTime != "" {
		if endTime, parseErr := utils.ParseTimestamp(req.ExpectEndTime); parseErr == nil {
			t := time.Unix(endTime, 0)
			entryOrder.ExpectEndTime = &t
		}
	}

	// 设置当前时间为订单创建时间
	now := time.Now()
	entryOrder.OrderCreateTime = &now

	// 创建入库单
	if err := l.svcCtx.EntryOrderRepo.Create(l.ctx, entryOrder); err != nil {
		l.Errorf("创建入库单失败: %v", err)
		if warehouseErr := errors.GetWarehouseError(err); warehouseErr != nil {
			return &types.CreateEntryOrderResp{
				BaseMsgResp: types.BaseMsgResp{
					Code: warehouseErr.Code,
					Msg:  warehouseErr.Message,
				},
			}, nil
		}
		return &types.CreateEntryOrderResp{
			BaseMsgResp: types.BaseMsgResp{
				Code: constants.ErrCodeDatabaseError,
				Msg:  constants.ErrMsgDatabaseError,
			},
		}, nil
	}

	// 创建入库单明细
	if len(req.OrderLines) > 0 {
		lines := make([]*model.EntryOrderLine, 0, len(req.OrderLines))
		for _, lineReq := range req.OrderLines {
			line := &model.EntryOrderLine{
				EntryOrderID:   entryOrder.ID,
				EntryOrderCode: entryOrder.EntryOrderCode,
				OwnerCode:      constants.DefaultOwnerCode,
				ItemCode:       lineReq.ItemCode,
				ItemName:       lineReq.ItemName,
				PlanQty:        lineReq.PlanQty,
				PurchasePrice:  lineReq.PurchasePrice,
				RetailPrice:    lineReq.RetailPrice,
				BatchCode:      lineReq.BatchCode,
				ProduceCode:    lineReq.ProduceCode,
				BoxNumber:      lineReq.BoxNumber,
				PalletNumber:   lineReq.PalletNumber,
				Remark:         lineReq.Remark,
				InventoryType:  constants.InventoryTypeZP,
				Unit:           constants.DefaultUnit,
				DeviceStatus:   constants.DeviceStatusNormal,
			}

			// 解析日期字段
			if lineReq.ProductDate != "" {
				if productDate, parseErr := utils.ParseTimestamp(lineReq.ProductDate); parseErr == nil {
					t := time.Unix(productDate, 0)
					line.ProductDate = &t
				}
			}
			if lineReq.ExpireDate != "" {
				if expireDate, parseErr := utils.ParseTimestamp(lineReq.ExpireDate); parseErr == nil {
					t := time.Unix(expireDate, 0)
					line.ExpireDate = &t
				}
			}

			// 设置SN编码
			if len(lineReq.SnCodes) > 0 {
				line.SnCodes = model.JSONArray(lineReq.SnCodes)
			}

			lines = append(lines, line)
		}

		// 批量创建明细
		if err := l.svcCtx.EntryOrderLineRepo.CreateLines(l.ctx, lines); err != nil {
			l.Errorf("创建入库明细失败: %v", err)
			// 这里可以考虑回滚入库单，但为了简化，暂时只记录错误
			return &types.CreateEntryOrderResp{
				BaseMsgResp: types.BaseMsgResp{
					Code: constants.ErrCodeDatabaseError,
					Msg:  "创建入库明细失败",
				},
			}, nil
		}

		// 更新入库单统计信息
		entryOrder.TotalOrderLines = int32(len(lines))
		entryOrder.ExpectedQuantity = 0
		for _, line := range lines {
			entryOrder.ExpectedQuantity += line.PlanQty
		}
	}

	l.Infof("创建入库单成功: ID=%d, Code=%s", entryOrder.ID, entryOrder.EntryOrderCode)

	return &types.CreateEntryOrderResp{
		BaseMsgResp: types.BaseMsgResp{
			Code: constants.ErrCodeSuccess,
			Msg:  constants.ErrMsgSuccess,
		},
		Data: types.CreateEntryOrderData{
			Id:             entryOrder.ID,
			EntryOrderCode: entryOrder.EntryOrderCode,
		},
	}, nil
}

// validateCreateRequest 验证创建入库单请求参数
func (l *CreateEntryOrderLogic) validateCreateRequest(req *types.CreateEntryOrderReq) error {
	if req == nil {
		return errors.ErrInvalidParam.WithDetail("请求参数不能为空")
	}
	if req.WarehouseCode == "" {
		return errors.ErrInvalidParam.WithDetail("云仓编码不能为空")
	}
	if req.SupplierCode == "" {
		return errors.ErrInvalidParam.WithDetail("供应商编码不能为空")
	}
	if req.SupplierName == "" {
		return errors.ErrInvalidParam.WithDetail("供应商名称不能为空")
	}
	if len(req.OrderLines) == 0 {
		return errors.ErrInvalidParam.WithDetail("入库明细不能为空")
	}

	// 验证明细
	for i, line := range req.OrderLines {
		if !utils.ValidateItemCode(line.ItemCode) {
			return errors.ErrInvalidParam.WithDetail(fmt.Sprintf("第%d行商品编码格式无效", i+1))
		}
		if line.ItemName == "" {
			return errors.ErrInvalidParam.WithDetail(fmt.Sprintf("第%d行商品名称不能为空", i+1))
		}
		if line.PlanQty <= 0 {
			return errors.ErrInvalidParam.WithDetail(fmt.Sprintf("第%d行计划数量必须大于0", i+1))
		}
	}

	return nil
}
