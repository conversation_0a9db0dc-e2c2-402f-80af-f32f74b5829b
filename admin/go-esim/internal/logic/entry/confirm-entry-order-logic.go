package entry

import (
	"context"
	"fmt"
	"time"

	"github.com/Wenpiner/iot-api/internal/model"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
	"github.com/Wenpiner/iot-api/pkg/constants"
	"github.com/Wenpiner/iot-api/pkg/errors"
	"github.com/Wenpiner/iot-api/pkg/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type ConfirmEntryOrderLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewConfirmEntryOrderLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ConfirmEntryOrderLogic {
	return &ConfirmEntryOrderLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *ConfirmEntryOrderLogic) ConfirmEntryOrder(req *types.ConfirmEntryOrderReq) (resp *types.ConfirmEntryOrderResp, err error) {
	// 参数验证
	if err := l.validateConfirmRequest(req); err != nil {
		l.Errorf("确认入库参数验证失败: %v", err)
		return &types.ConfirmEntryOrderResp{
			BaseMsgResp: types.BaseMsgResp{
				Code: constants.ErrCodeInvalidParam,
				Msg:  err.Error(),
			},
		}, nil
	}

	// 查询入库单信息
	entryOrder, err := l.svcCtx.EntryOrderRepo.GetByID(l.ctx, req.Id)
	if err != nil {
		l.Errorf("查询入库单失败: %v", err)
		if warehouseErr := errors.GetWarehouseError(err); warehouseErr != nil {
			return &types.ConfirmEntryOrderResp{
				BaseMsgResp: types.BaseMsgResp{
					Code: warehouseErr.Code,
					Msg:  warehouseErr.Message,
				},
			}, nil
		}
		return &types.ConfirmEntryOrderResp{
			BaseMsgResp: types.BaseMsgResp{
				Code: constants.ErrCodeDatabaseError,
				Msg:  constants.ErrMsgDatabaseError,
			},
		}, nil
	}

	// 检查订单状态
	if entryOrder.OrderStatus != constants.EntryOrderStatusPending &&
		entryOrder.OrderStatus != constants.EntryOrderStatusPartial {
		l.Errorf("入库单状态不允许确认: %d", entryOrder.OrderStatus)
		return &types.ConfirmEntryOrderResp{
			BaseMsgResp: types.BaseMsgResp{
				Code: constants.ErrCodeOrderStatusInvalid,
				Msg:  "入库单状态不允许确认操作",
			},
		}, nil
	}

	// 更新实际到货时间
	if req.ActualArrivalTime != "" {
		if arrivalTime, parseErr := utils.ParseTimestamp(req.ActualArrivalTime); parseErr == nil {
			if updateErr := l.svcCtx.EntryOrderRepo.UpdateActualArrivalTime(l.ctx, req.Id, time.Unix(arrivalTime, 0)); updateErr != nil {
				l.Errorf("更新到货时间失败: %v", updateErr)
			}
		}
	}

	// 更新入库明细
	if len(req.OrderLines) > 0 {
		for _, lineReq := range req.OrderLines {
			line := &model.EntryOrderLine{
				ID:            lineReq.Id,
				ActualQty:     lineReq.ActualQty,
				ShelfLocation: lineReq.ShelfLocation,
				DeviceStatus:  lineReq.DeviceStatus,
				Remark:        lineReq.Remark,
			}

			// 设置入库时间为当前时间
			now := time.Now()
			line.InboundTime = &now

			if err := l.svcCtx.EntryOrderLineRepo.UpdateLine(l.ctx, line); err != nil {
				l.Errorf("更新入库明细失败: %v", err)
				return &types.ConfirmEntryOrderResp{
					BaseMsgResp: types.BaseMsgResp{
						Code: constants.ErrCodeDatabaseError,
						Msg:  "更新入库明细失败",
					},
				}, nil
			}
		}
	}

	// 更新入库单数量统计
	if err := l.svcCtx.EntryOrderRepo.UpdateQuantities(l.ctx, req.Id); err != nil {
		l.Errorf("更新入库单数量统计失败: %v", err)
		return &types.ConfirmEntryOrderResp{
			BaseMsgResp: types.BaseMsgResp{
				Code: constants.ErrCodeDatabaseError,
				Msg:  "更新入库单数量统计失败",
			},
		}, nil
	}

	// 重新查询入库单以获取最新数据
	updatedOrder, err := l.svcCtx.EntryOrderRepo.GetByID(l.ctx, req.Id)
	if err != nil {
		l.Errorf("重新查询入库单失败: %v", err)
		return &types.ConfirmEntryOrderResp{
			BaseMsgResp: types.BaseMsgResp{
				Code: constants.ErrCodeDatabaseError,
				Msg:  constants.ErrMsgDatabaseError,
			},
		}, nil
	}

	// 判断入库状态
	var newStatus int32
	if updatedOrder.ActualQuantity == 0 {
		newStatus = constants.EntryOrderStatusPending // 待入库
	} else if updatedOrder.ActualQuantity < updatedOrder.ExpectedQuantity {
		newStatus = constants.EntryOrderStatusPartial // 部分入库
	} else {
		newStatus = constants.EntryOrderStatusCompleted // 全部入库
	}

	// 更新入库单状态
	operatorName := req.OperatorName
	if operatorName == "" {
		operatorName = "系统"
	}

	remark := req.Remark
	if remark == "" {
		remark = "确认入库"
	}

	if err := l.svcCtx.EntryOrderRepo.UpdateStatus(l.ctx, req.Id, newStatus, operatorName, remark); err != nil {
		l.Errorf("更新入库单状态失败: %v", err)
		return &types.ConfirmEntryOrderResp{
			BaseMsgResp: types.BaseMsgResp{
				Code: constants.ErrCodeDatabaseError,
				Msg:  "更新入库单状态失败",
			},
		}, nil
	}

	l.Infof("确认入库成功: ID=%d, Status=%d", req.Id, newStatus)

	return &types.ConfirmEntryOrderResp{
		BaseMsgResp: types.BaseMsgResp{
			Code: constants.ErrCodeSuccess,
			Msg:  constants.ErrMsgSuccess,
		},
	}, nil
}

// validateConfirmRequest 验证确认入库请求参数
func (l *ConfirmEntryOrderLogic) validateConfirmRequest(req *types.ConfirmEntryOrderReq) error {
	if req == nil {
		return errors.ErrInvalidParam.WithDetail("请求参数不能为空")
	}
	if req.Id <= 0 {
		return errors.ErrInvalidParam.WithDetail("入库单ID无效")
	}

	// 验证明细参数
	for i, line := range req.OrderLines {
		if line.Id <= 0 {
			return errors.ErrInvalidParam.WithDetail(fmt.Sprintf("第%d行明细ID无效", i+1))
		}
		if line.ActualQty < 0 {
			return errors.ErrInvalidParam.WithDetail(fmt.Sprintf("第%d行实际数量不能小于0", i+1))
		}
	}

	return nil
}
