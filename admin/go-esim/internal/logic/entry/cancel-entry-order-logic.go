package entry

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
	"github.com/Wenpiner/iot-api/pkg/constants"
	"github.com/Wenpiner/iot-api/pkg/errors"

	"github.com/zeromicro/go-zero/core/logx"
)

type CancelEntryOrderLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCancelEntryOrderLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CancelEntryOrderLogic {
	return &CancelEntryOrderLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *CancelEntryOrderLogic) CancelEntryOrder(req *types.CancelEntryOrderReq) (resp *types.CancelEntryOrderResp, err error) {
	// 参数验证
	if err := l.validateCancelRequest(req); err != nil {
		l.<PERSON>("取消入库单参数验证失败: %v", err)
		return &types.CancelEntryOrderResp{
			BaseMsgResp: types.BaseMsgResp{
				Code: constants.ErrCodeInvalidParam,
				Msg:  err.Error(),
			},
		}, nil
	}

	// 查询入库单信息
	entryOrder, err := l.svcCtx.EntryOrderRepo.GetByID(l.ctx, req.Id)
	if err != nil {
		l.Errorf("查询入库单失败: %v", err)
		if warehouseErr := errors.GetWarehouseError(err); warehouseErr != nil {
			return &types.CancelEntryOrderResp{
				BaseMsgResp: types.BaseMsgResp{
					Code: warehouseErr.Code,
					Msg:  warehouseErr.Message,
				},
			}, nil
		}
		return &types.CancelEntryOrderResp{
			BaseMsgResp: types.BaseMsgResp{
				Code: constants.ErrCodeDatabaseError,
				Msg:  constants.ErrMsgDatabaseError,
			},
		}, nil
	}

	// 检查订单状态是否允许取消
	if entryOrder.OrderStatus != constants.EntryOrderStatusPending &&
		entryOrder.OrderStatus != constants.EntryOrderStatusPartial {
		l.Errorf("入库单状态不允许取消: %d", entryOrder.OrderStatus)
		return &types.CancelEntryOrderResp{
			BaseMsgResp: types.BaseMsgResp{
				Code: constants.ErrCodeOrderStatusInvalid,
				Msg:  "入库单状态不允许取消操作",
			},
		}, nil
	}

	// 更新入库单状态为取消
	operatorName := req.OperatorName
	if operatorName == "" {
		operatorName = "系统"
	}

	remark := "取消原因: " + req.CancelReason

	if err := l.svcCtx.EntryOrderRepo.UpdateStatus(l.ctx, req.Id, constants.EntryOrderStatusCancelled, operatorName, remark); err != nil {
		l.Errorf("取消入库单失败: %v", err)
		if warehouseErr := errors.GetWarehouseError(err); warehouseErr != nil {
			return &types.CancelEntryOrderResp{
				BaseMsgResp: types.BaseMsgResp{
					Code: warehouseErr.Code,
					Msg:  warehouseErr.Message,
				},
			}, nil
		}
		return &types.CancelEntryOrderResp{
			BaseMsgResp: types.BaseMsgResp{
				Code: constants.ErrCodeDatabaseError,
				Msg:  "取消入库单失败",
			},
		}, nil
	}

	l.Infof("取消入库单成功: ID=%d, Reason=%s", req.Id, req.CancelReason)

	return &types.CancelEntryOrderResp{
		BaseMsgResp: types.BaseMsgResp{
			Code: constants.ErrCodeSuccess,
			Msg:  constants.ErrMsgSuccess,
		},
	}, nil
}

// validateCancelRequest 验证取消入库单请求参数
func (l *CancelEntryOrderLogic) validateCancelRequest(req *types.CancelEntryOrderReq) error {
	if req == nil {
		return errors.ErrInvalidParam.WithDetail("请求参数不能为空")
	}
	if req.Id <= 0 {
		return errors.ErrInvalidParam.WithDetail("入库单ID无效")
	}
	if req.CancelReason == "" {
		return errors.ErrInvalidParam.WithDetail("取消原因不能为空")
	}

	return nil
}
