package entry

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/model"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
	"github.com/Wenpiner/iot-api/pkg/constants"
	"github.com/Wenpiner/iot-api/pkg/errors"
	"github.com/Wenpiner/iot-api/pkg/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetEntryOrderListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetEntryOrderListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetEntryOrderListLogic {
	return &GetEntryOrderListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *GetEntryOrderListLogic) GetEntryOrderList(req *types.GetEntryOrderListReq) (resp *types.GetEntryOrderListResp, err error) {
	// 构建查询参数
	query := &model.EntryOrderListQuery{
		Page:          req.Page,
		PageSize:      req.PageSize,
		WarehouseCode: req.WarehouseCode,
		OrderStatus:   req.OrderStatus,
		SupplierCode:  req.SupplierCode,
		StartTime:     req.StartTime,
		EndTime:       req.EndTime,
		Keyword:       req.Keyword,
	}

	// 查询入库单列表
	result, err := l.svcCtx.EntryOrderRepo.GetList(l.ctx, query)
	if err != nil {
		l.Errorf("查询入库单列表失败: %v", err)

		// 处理业务错误
		if warehouseErr := errors.GetWarehouseError(err); warehouseErr != nil {
			return &types.GetEntryOrderListResp{
				BaseMsgResp: types.BaseMsgResp{
					Code: warehouseErr.Code,
					Msg:  warehouseErr.Message,
				},
			}, nil
		}

		// 系统错误
		return &types.GetEntryOrderListResp{
			BaseMsgResp: types.BaseMsgResp{
				Code: constants.ErrCodeDatabaseError,
				Msg:  constants.ErrMsgDatabaseError,
			},
		}, nil
	}

	// 转换数据格式
	entryOrders := make([]types.EntryOrder, 0, len(result.List))
	for _, order := range result.List {
		entryOrder := types.EntryOrder{
			Id:                order.ID,
			EntryOrderCode:    order.EntryOrderCode,
			WarehouseCode:     order.WarehouseCode,
			WarehouseName:     order.WarehouseName,
			OwnerCode:         order.OwnerCode,
			OrderType:         order.OrderType,
			PurchaseOrderCode: order.PurchaseOrderCode,
			LogisticsCode:     order.LogisticsCode,
			LogisticsName:     order.LogisticsName,
			ExpressCode:       order.ExpressCode,
			SupplierCode:      order.SupplierCode,
			SupplierName:      order.SupplierName,
			OperatorCode:      order.OperatorCode,
			OperatorName:      order.OperatorName,
			TotalOrderLines:   order.TotalOrderLines,
			ExpectedQuantity:  order.ExpectedQuantity,
			ActualQuantity:    order.ActualQuantity,
			OrderStatus:       order.OrderStatus,
			Remark:            order.Remark,
			QimenEntryOrderID: order.QimenEntryOrderID,
			ApiSyncStatus:     order.ApiSyncStatus,
			ApiErrorMessage:   order.ApiErrorMessage,
			CreatedAt:         order.CreatedAt.Unix(),
			UpdatedAt:         order.UpdatedAt.Unix(),
		}

		// 转换时间字段
		if order.OrderCreateTime != nil {
			entryOrder.CreatedAt = order.OrderCreateTime.Unix()
		}
		if order.ExpectStartTime != nil {
			entryOrder.ExpectStartTime = utils.FormatTimestamp(order.ExpectStartTime.Unix())
		}
		if order.ExpectEndTime != nil {
			entryOrder.ExpectEndTime = utils.FormatTimestamp(order.ExpectEndTime.Unix())
		}
		if order.ActualArrivalTime != nil {
			entryOrder.ActualArrivalTime = utils.FormatTimestamp(order.ActualArrivalTime.Unix())
		}
		if order.OperateTime != nil {
			entryOrder.OperateTime = utils.FormatTimestamp(order.OperateTime.Unix())
		}
		if order.ApiSyncTime != nil {
			entryOrder.ApiSyncTime = utils.FormatTimestamp(order.ApiSyncTime.Unix())
		}

		entryOrders = append(entryOrders, entryOrder)
	}

	return &types.GetEntryOrderListResp{
		BaseMsgResp: types.BaseMsgResp{
			Code: constants.ErrCodeSuccess,
			Msg:  constants.ErrMsgSuccess,
		},
		Data: types.EntryOrderListData{
			List: entryOrders,
			PageInfo: types.PageInfo{
				Page:     uint64(query.Page),
				PageSize: uint64(query.PageSize),
			},
		},
	}, nil
}
