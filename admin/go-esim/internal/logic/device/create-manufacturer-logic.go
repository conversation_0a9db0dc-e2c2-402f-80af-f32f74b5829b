package device

import (
	"context"
	"fmt"

	"github.com/Wenpiner/iot-api/internal/model"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
	"github.com/Wenpiner/iot-api/pkg/operation"
	"github.com/duke-git/lancet/v2/pointer"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateManufacturerLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateManufacturerLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateManufacturerLogic {
	return &CreateManufacturerLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *CreateManufacturerLogic) CreateManufacturer(req *types.ManufacturerCreateReq) (resp *types.ManufacturerCreateResp, err error) {
	// 创建厂商
	manufacturer := &model.DeviceManufacturer{
		ID:            pointer.UnwarpOrDefault(req.ID),
		Name:          req.Name,
		ContactPerson: req.ContactPerson,
		ContactPhone:  req.ContactPhone,
		ContactEmail:  req.ContactEmail,
		Address:       req.Address,
		BankAccount:   req.BankAccount,
		BankName:      req.BankName,
	}

	// 创建厂商
	if err := l.svcCtx.DeviceManufacturerRepo.Save(l.ctx, manufacturer); err != nil {
		return nil, err
	}

	var targetId int32
	if manufacturer.ID != 0 {
		targetId = int32(manufacturer.ID)
	}
	operation.DBLog(l.ctx, l.svcCtx.DB, "创建设备厂商", &targetId, "iot.device_manufacturer", fmt.Sprintf("厂商: %s", manufacturer.Name), map[string]interface{}{})

	// 厂商创建成功，发送新用户数据
	if err := l.svcCtx.SendNewUserData(l.ctx, uint32(manufacturer.ID), model.AssetTypeDeviceSupplier); err != nil {
		logx.Errorf("发送新用户数据失败: %v", err)
		return nil, err
	}

	return &types.ManufacturerCreateResp{
		BaseMsgResp: types.BaseMsgResp{
			Code: 0,
			Msg:  "创建成功",
		},
	}, nil
}
