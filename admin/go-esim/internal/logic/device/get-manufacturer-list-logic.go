package device

import (
	"context"
	"fmt"
	"time"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
	"github.com/georgysavva/scany/v2/pgxscan"
	"github.com/jackc/pgx/v5"
	"github.com/zeromicro/go-zero/core/logx"
)

type GetManufacturerListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetManufacturerListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetManufacturerListLogic {
	return &GetManufacturerListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *GetManufacturerListLogic) GetManufacturerList(req *types.ManufacturerListReq) (resp *types.ManufacturerListResp, err error) {
	resp = &types.ManufacturerListResp{
		BaseMsgResp: types.BaseMsgResp{
			Code: 0,
			Msg:  "success",
		},
	}

	// 构建查询条件
	whereClause := ""
	args := pgx.NamedArgs{}

	if req.Name != nil && *req.Name != "" {
		whereClause += " AND name ILIKE @name"
		args["name"] = "%" + *req.Name + "%"
	}

	if req.ContactPerson != nil && *req.ContactPerson != "" {
		whereClause += " AND contact_person ILIKE @contact_person"
		args["contact_person"] = "%" + *req.ContactPerson + "%"
	}

	if req.ContactPhone != nil && *req.ContactPhone != "" {
		whereClause += " AND contact_phone ILIKE @contact_phone"
		args["contact_phone"] = "%" + *req.ContactPhone + "%"
	}

	// 计算总数
	countQuery := fmt.Sprintf(`
		SELECT COUNT(*) 
		FROM iot.device_manufacturer 
		WHERE 1=1 %s
	`, whereClause)

	var total int64
	err = l.svcCtx.DB.QueryRow(l.ctx, countQuery, args).Scan(&total)
	if err != nil {
		return nil, err
	}

	// 查询数据
	query := fmt.Sprintf(`
		SELECT id, name, contact_person, contact_phone, contact_email,
			   address, bank_account, bank_name, created_at, updated_at
		FROM iot.device_manufacturer 
		WHERE 1=1 %s
		ORDER BY created_at DESC
		LIMIT @limit OFFSET @offset
	`, whereClause)

	args["limit"] = req.Page.PageSize
	args["offset"] = (req.Page.Page - 1) * req.Page.PageSize

	type ManufacturerRow struct {
		ID            int64     `db:"id"`
		Name          string    `db:"name"`
		ContactPerson string    `db:"contact_person"`
		ContactPhone  string    `db:"contact_phone"`
		ContactEmail  string    `db:"contact_email"`
		Address       string    `db:"address"`
		BankAccount   string    `db:"bank_account"`
		BankName      string    `db:"bank_name"`
		CreatedAt     time.Time `db:"created_at"`
		UpdatedAt     time.Time `db:"updated_at"`
	}

	var rows []ManufacturerRow
	if err := pgxscan.Select(l.ctx, l.svcCtx.DB, &rows, query, args); err != nil {
		return nil, err
	}

	manufacturers := make([]types.ManufacturerInfo, 0, len(rows))
	for _, row := range rows {
		manufacturer := types.ManufacturerInfo{
			ID:            row.ID,
			Name:          row.Name,
			ContactPerson: row.ContactPerson,
			ContactPhone:  row.ContactPhone,
			ContactEmail:  row.ContactEmail,
			Address:       row.Address,
			BankAccount:   row.BankAccount,
			BankName:      row.BankName,
			CreatedAt:     row.CreatedAt.Unix(),
			UpdatedAt:     row.UpdatedAt.Unix(),
		}
		manufacturers = append(manufacturers, manufacturer)
	}

	resp.Data = types.ManufacturerListInfo{
		BaseListInfo: types.BaseListInfo{
			Total: uint64(total),
		},
		Data: manufacturers,
	}

	return resp, nil
}
