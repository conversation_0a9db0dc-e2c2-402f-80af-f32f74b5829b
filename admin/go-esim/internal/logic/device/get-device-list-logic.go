package device

import (
	"context"
	"fmt"
	"time"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
	"github.com/georgysavva/scany/v2/pgxscan"
	"github.com/jackc/pgx/v5"
	"github.com/zeromicro/go-zero/core/logx"
)

type GetDeviceListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetDeviceListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDeviceListLogic {
	return &GetDeviceListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *GetDeviceListLogic) GetDeviceList(req *types.DeviceListReq) (resp *types.DeviceListResp, err error) {
	resp = &types.DeviceListResp{
		BaseMsgResp: types.BaseMsgResp{
			Code: 0,
			Msg:  "success",
		},
	}

	// 构建查询条件
	whereClause := ""
	args := pgx.NamedArgs{}

	// 设备ID（设备号）- 精确查询
	if req.DeviceNo != nil && *req.DeviceNo != "" {
		whereClause += " AND d.device_no = @device_no"
		args["device_no"] = *req.DeviceNo
	}

	// 设备IMEI - 精确查询
	if req.Imei != nil && *req.Imei != "" {
		whereClause += " AND d.imei = @imei"
		args["imei"] = *req.Imei
	}

	// 卡的ICCID - 通过关联卡片查询
	if req.Iccid != nil && *req.Iccid != "" {
		whereClause += " AND c.iccid = @iccid"
		args["iccid"] = *req.Iccid
	}

	// 设备标签 - 数组包含查询
	if len(req.Tags) > 0 {
		whereClause += " AND d.tags && @tags"
		args["tags"] = req.Tags
	}

	// 所属渠道商ID
	if req.ChannelId != nil {
		whereClause += " AND d.channel_id = @channel_id"
		args["channel_id"] = *req.ChannelId
	}

	// 创建时间范围查询
	if req.CreatedAtStart != nil {
		whereClause += " AND d.created_at >= @created_at_start"
		args["created_at_start"] = time.Unix(*req.CreatedAtStart, 0)
	}
	if req.CreatedAtEnd != nil {
		whereClause += " AND d.created_at <= @created_at_end"
		args["created_at_end"] = time.Unix(*req.CreatedAtEnd, 0)
	}

	// 打包时间范围查询
	if req.PackageDateStart != nil {
		whereClause += " AND d.package_date >= @package_date_start"
		args["package_date_start"] = time.Unix(*req.PackageDateStart, 0)
	}
	if req.PackageDateEnd != nil {
		whereClause += " AND d.package_date <= @package_date_end"
		args["package_date_end"] = time.Unix(*req.PackageDateEnd, 0)
	}

	// 计算总数
	countQuery := fmt.Sprintf(`
		SELECT COUNT(DISTINCT d.id)
		FROM iot.devices d
		LEFT JOIN iot.cards c ON d.id = c.device_id
		LEFT JOIN iot.card_channels cc ON d.channel_id = cc.id
		WHERE 1=1 %s
	`, whereClause)

	var total int64
	err = l.svcCtx.DB.QueryRow(l.ctx, countQuery, args).Scan(&total)
	if err != nil {
		return nil, err
	}

	// 查询数据 - 复杂JOIN查询获取设备、卡片、渠道信息
	query := fmt.Sprintf(`
		SELECT
			d.id, d.box_no, d.random_code, d.package_date, d.device_no,
			d.imei, d.imsi, d.mac, d.msisdn, d.ccid, d.access_number,
			d.sn, d.ssid, d.wifi_key, d.wifi_mac, d.wifi_mac_5g,
			d.created_at, d.updated_at, d.speed_up_link, d.speed_down_link,
			d.hidden, d.tags, d.cost_price,
			-- 渠道信息
			cc.id as channel_id, cc.name as channel_name,
			cc.operator as channel_operator, cc.channel_type as channel_type,
			-- 卡片信息
			c.id as card_id, c.iccid as card_iccid, c.msisdn as card_msisdn,
			c.imsi as card_imsi, c.status as card_status, c.activated_at as card_activated_at
		FROM iot.devices d
		LEFT JOIN iot.card_channels cc ON d.channel_id = cc.id
		LEFT JOIN iot.cards c ON d.id = c.device_id
		WHERE 1=1 %s
		ORDER BY d.created_at DESC
		LIMIT @limit OFFSET @offset
	`, whereClause)

	args["limit"] = req.Page.PageSize
	args["offset"] = (req.Page.Page - 1) * req.Page.PageSize

	// 定义数据行结构
	type DeviceRow struct {
		ID            int64      `db:"id"`
		BoxNo         string     `db:"box_no"`
		RandomCode    *string    `db:"random_code"`
		PackageDate   *time.Time `db:"package_date"`
		DeviceNo      string     `db:"device_no"`
		Imei          *string    `db:"imei"`
		Imsi          string     `db:"imsi"`
		Mac           *string    `db:"mac"`
		Msisdn        string     `db:"msisdn"`
		Ccid          string     `db:"ccid"`
		AccessNumber  string     `db:"access_number"`
		Sn            *string    `db:"sn"`
		Ssid          *string    `db:"ssid"`
		WifiKey       *string    `db:"wifi_key"`
		WifiMac       *string    `db:"wifi_mac"`
		WifiMac5g     string     `db:"wifi_mac_5g"`
		CreatedAt     time.Time  `db:"created_at"`
		UpdatedAt     time.Time  `db:"updated_at"`
		SpeedUpLink   int32      `db:"speed_up_link"`
		SpeedDownLink int32      `db:"speed_down_link"`
		Hidden        int32      `db:"hidden"`
		Tags          []string   `db:"tags"`
		CostPrice     float64    `db:"cost_price"`
		// 渠道信息
		ChannelID       *int64  `db:"channel_id"`
		ChannelName     *string `db:"channel_name"`
		ChannelOperator *string `db:"channel_operator"`
		ChannelType     *string `db:"channel_type"`
		// 卡片信息
		CardID          *int64     `db:"card_id"`
		CardIccid       *string    `db:"card_iccid"`
		CardMsisdn      *string    `db:"card_msisdn"`
		CardImsi        *string    `db:"card_imsi"`
		CardStatus      *int32     `db:"card_status"`
		CardActivatedAt *time.Time `db:"card_activated_at"`
	}

	var rows []DeviceRow
	if err := pgxscan.Select(l.ctx, l.svcCtx.DB, &rows, query, args); err != nil {
		return nil, err
	}

	// 转换数据格式
	devices := make([]types.DeviceInfo, 0, len(rows))
	for _, row := range rows {
		device := types.DeviceInfo{
			ID:            row.ID,
			BoxNo:         row.BoxNo,
			RandomCode:    safeStringValue(row.RandomCode),
			PackageDate:   safeTimeToUnix(row.PackageDate),
			DeviceNo:      row.DeviceNo,
			Imei:          safeStringValue(row.Imei),
			Imsi:          row.Imsi,
			Mac:           safeStringValue(row.Mac),
			Msisdn:        row.Msisdn,
			Ccid:          row.Ccid,
			AccessNumber:  row.AccessNumber,
			Sn:            safeStringValue(row.Sn),
			Ssid:          safeStringValue(row.Ssid),
			WifiKey:       safeStringValue(row.WifiKey),
			WifiMac:       safeStringValue(row.WifiMac),
			WifiMac5g:     row.WifiMac5g,
			CreatedAt:     row.CreatedAt.Unix(),
			UpdatedAt:     row.UpdatedAt.Unix(),
			SpeedUpLink:   row.SpeedUpLink,
			SpeedDownLink: row.SpeedDownLink,
			Hidden:        row.Hidden,
			Tags:          row.Tags,
			CostPrice:     row.CostPrice,
		}

		// 设置渠道信息
		if row.ChannelID != nil {
			device.Channel = &types.DeviceChannelInfo{
				ID:          *row.ChannelID,
				Name:        safeStringValue(row.ChannelName),
				Operator:    safeStringValue(row.ChannelOperator),
				ChannelType: safeStringValue(row.ChannelType),
			}
		}

		// 设置卡片信息
		if row.CardID != nil {
			device.Card = &types.DeviceCardInfo{
				ID:          *row.CardID,
				Iccid:       safeStringValue(row.CardIccid),
				Msisdn:      safeStringValue(row.CardMsisdn),
				Imsi:        safeStringValue(row.CardImsi),
				Status:      safeInt32Value(row.CardStatus),
				ActivatedAt: safeTimeToUnix(row.CardActivatedAt),
			}
		}

		devices = append(devices, device)
	}

	resp.Data = types.DeviceListInfo{
		BaseListInfo: types.BaseListInfo{
			Total: uint64(total),
		},
		Data: devices,
	}

	return resp, nil
}

// 辅助函数：安全地获取字符串指针的值
func safeStringValue(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

// 辅助函数：安全地获取int32指针的值
func safeInt32Value(i *int32) int32 {
	if i == nil {
		return 0
	}
	return *i
}

// 辅助函数：安全地将时间指针转换为Unix时间戳
func safeTimeToUnix(t *time.Time) int64 {
	if t == nil {
		return 0
	}
	return t.Unix()
}
