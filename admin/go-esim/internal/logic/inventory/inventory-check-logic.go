package inventory

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type InventoryCheckLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewInventoryCheckLogic(ctx context.Context, svcCtx *svc.ServiceContext) *InventoryCheckLogic {
	return &InventoryCheckLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *InventoryCheckLogic) InventoryCheck(req *types.InventoryCheckReq) (resp *types.InventoryCheckResp, err error) {
	// todo: add your logic here and delete this line

	return
}
