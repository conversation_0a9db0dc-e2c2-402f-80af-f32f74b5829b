package inventory

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetInventoryLogsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetInventoryLogsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetInventoryLogsLogic {
	return &GetInventoryLogsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *GetInventoryLogsLogic) GetInventoryLogs(req *types.GetInventoryLogsReq) (resp *types.GetInventoryLogsResp, err error) {
	// todo: add your logic here and delete this line

	return
}
