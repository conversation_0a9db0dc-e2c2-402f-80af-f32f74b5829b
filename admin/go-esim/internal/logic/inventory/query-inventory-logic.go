package inventory

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type QueryInventoryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewQueryInventoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *QueryInventoryLogic {
	return &QueryInventoryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *QueryInventoryLogic) QueryInventory(req *types.QueryInventoryReq) (resp *types.QueryInventoryResp, err error) {
	// todo: add your logic here and delete this line

	return
}
