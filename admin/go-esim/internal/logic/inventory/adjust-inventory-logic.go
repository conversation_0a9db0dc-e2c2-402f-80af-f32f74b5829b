package inventory

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type AdjustInventoryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAdjustInventoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdjustInventoryLogic {
	return &AdjustInventoryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *AdjustInventoryLogic) AdjustInventory(req *types.AdjustInventoryReq) (resp *types.AdjustInventoryResp, err error) {
	// todo: add your logic here and delete this line

	return
}
