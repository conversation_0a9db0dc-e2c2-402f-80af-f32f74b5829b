package warehouse

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/model"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
	"github.com/Wenpiner/iot-api/pkg/constants"
	"github.com/Wenpiner/iot-api/pkg/errors"
	"github.com/Wenpiner/iot-api/pkg/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateWarehouseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateWarehouseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateWarehouseLogic {
	return &CreateWarehouseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *CreateWarehouseLogic) CreateWarehouse(req *types.CreateWarehouseReq) (resp *types.CreateWarehouseResp, err error) {
	// 参数验证
	if err := l.validateCreateRequest(req); err != nil {
		l.Errorf("创建云仓参数验证失败: %v", err)
		return &types.CreateWarehouseResp{
			BaseMsgResp: types.BaseMsgResp{
				Code: constants.ErrCodeInvalidParam,
				Msg:  err.Error(),
			},
		}, nil
	}

	// 构建云仓模型
	warehouse := &model.Warehouse{
		WarehouseCode: req.WarehouseCode,
		WarehouseName: req.WarehouseName,
		CompanyName:   req.CompanyName,
		ContactPerson: req.ContactPerson,
		ContactPhone:  req.ContactPhone,
		ContactEmail:  req.ContactEmail,
		Address:       req.Address,
		Province:      req.Province,
		City:          req.City,
		District:      req.District,
		ApiEndpoint:   req.ApiEndpoint,
		AppKey:        req.AppKey,
		AppSecret:     req.AppSecret,
		CustomerID:    req.CustomerID,
		Status:        constants.WarehouseStatusNormal, // 默认启用
	}

	// 创建云仓
	if err := l.svcCtx.WarehouseRepo.Create(l.ctx, warehouse); err != nil {
		l.Errorf("创建云仓失败: %v", err)

		// 处理业务错误
		if warehouseErr := errors.GetWarehouseError(err); warehouseErr != nil {
			return &types.CreateWarehouseResp{
				BaseMsgResp: types.BaseMsgResp{
					Code: warehouseErr.Code,
					Msg:  warehouseErr.Message,
				},
			}, nil
		}

		// 系统错误
		return &types.CreateWarehouseResp{
			BaseMsgResp: types.BaseMsgResp{
				Code: constants.ErrCodeDatabaseError,
				Msg:  constants.ErrMsgDatabaseError,
			},
		}, nil
	}

	l.Infof("创建云仓成功: ID=%d, Code=%s", warehouse.ID, warehouse.WarehouseCode)

	return &types.CreateWarehouseResp{
		BaseMsgResp: types.BaseMsgResp{
			Code: constants.ErrCodeSuccess,
			Msg:  constants.ErrMsgSuccess,
		},
		Data: types.CreateWarehouseData{
			Id: warehouse.ID,
		},
	}, nil
}

// validateCreateRequest 验证创建请求参数
func (l *CreateWarehouseLogic) validateCreateRequest(req *types.CreateWarehouseReq) error {
	if req == nil {
		return errors.ErrInvalidParam.WithDetail("请求参数不能为空")
	}
	if !utils.ValidateWarehouseCode(req.WarehouseCode) {
		return errors.ErrInvalidParam.WithDetail("云仓编码格式无效")
	}
	if req.WarehouseName == "" {
		return errors.ErrInvalidParam.WithDetail("云仓名称不能为空")
	}
	if req.CompanyName == "" {
		return errors.ErrInvalidParam.WithDetail("公司名称不能为空")
	}
	if req.ContactPerson == "" {
		return errors.ErrInvalidParam.WithDetail("联系人不能为空")
	}
	if !utils.ValidatePhone(req.ContactPhone) {
		return errors.ErrInvalidParam.WithDetail("联系电话格式无效")
	}
	if !utils.ValidateEmail(req.ContactEmail) {
		return errors.ErrInvalidParam.WithDetail("邮箱格式无效")
	}
	if req.Address == "" {
		return errors.ErrInvalidParam.WithDetail("地址不能为空")
	}
	if req.Province == "" {
		return errors.ErrInvalidParam.WithDetail("省份不能为空")
	}
	if req.City == "" {
		return errors.ErrInvalidParam.WithDetail("城市不能为空")
	}
	if req.ApiEndpoint == "" {
		return errors.ErrInvalidParam.WithDetail("API接口地址不能为空")
	}
	if req.AppKey == "" {
		return errors.ErrInvalidParam.WithDetail("AppKey不能为空")
	}
	if req.AppSecret == "" {
		return errors.ErrInvalidParam.WithDetail("AppSecret不能为空")
	}
	if req.CustomerID == "" {
		return errors.ErrInvalidParam.WithDetail("客户ID不能为空")
	}

	return nil
}
