package warehouse

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
	"github.com/Wenpiner/iot-api/pkg/constants"
	"github.com/Wenpiner/iot-api/pkg/errors"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetWarehouseDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetWarehouseDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetWarehouseDetailLogic {
	return &GetWarehouseDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *GetWarehouseDetailLogic) GetWarehouseDetail(req *types.GetWarehouseDetailReq) (resp *types.GetWarehouseDetailResp, err error) {
	// 参数验证
	if req.Id <= 0 {
		l.<PERSON>("云仓ID无效: %d", req.Id)
		return &types.GetWarehouseDetailResp{
			BaseMsgResp: types.BaseMsgResp{
				Code: constants.ErrCodeInvalidParam,
				Msg:  "云仓ID无效",
			},
		}, nil
	}

	// 查询云仓详情
	warehouse, err := l.svcCtx.WarehouseRepo.GetByID(l.ctx, req.Id)
	if err != nil {
		l.Errorf("查询云仓详情失败: %v", err)

		// 处理业务错误
		if warehouseErr := errors.GetWarehouseError(err); warehouseErr != nil {
			return &types.GetWarehouseDetailResp{
				BaseMsgResp: types.BaseMsgResp{
					Code: warehouseErr.Code,
					Msg:  warehouseErr.Message,
				},
			}, nil
		}

		// 系统错误
		return &types.GetWarehouseDetailResp{
			BaseMsgResp: types.BaseMsgResp{
				Code: constants.ErrCodeDatabaseError,
				Msg:  constants.ErrMsgDatabaseError,
			},
		}, nil
	}

	// 转换数据格式
	warehouseData := types.Warehouse{
		Id:            warehouse.ID,
		WarehouseCode: warehouse.WarehouseCode,
		WarehouseName: warehouse.WarehouseName,
		CompanyName:   warehouse.CompanyName,
		ContactPerson: warehouse.ContactPerson,
		ContactPhone:  warehouse.ContactPhone,
		ContactEmail:  warehouse.ContactEmail,
		Address:       warehouse.Address,
		Province:      warehouse.Province,
		City:          warehouse.City,
		District:      warehouse.District,
		ApiEndpoint:   warehouse.ApiEndpoint,
		AppKey:        warehouse.AppKey,
		CustomerID:    warehouse.CustomerID,
		Status:        warehouse.Status,
		CreatedAt:     warehouse.CreatedAt.Unix(),
		UpdatedAt:     warehouse.UpdatedAt.Unix(),
	}

	return &types.GetWarehouseDetailResp{
		BaseMsgResp: types.BaseMsgResp{
			Code: constants.ErrCodeSuccess,
			Msg:  constants.ErrMsgSuccess,
		},
		Data: warehouseData,
	}, nil
}
