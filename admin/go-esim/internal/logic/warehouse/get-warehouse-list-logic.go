package warehouse

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/model"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
	"github.com/Wenpiner/iot-api/pkg/constants"
	"github.com/Wenpiner/iot-api/pkg/errors"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetWarehouseListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetWarehouseListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetWarehouseListLogic {
	return &GetWarehouseListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *GetWarehouseListLogic) GetWarehouseList(req *types.GetWarehouseListReq) (resp *types.GetWarehouseListResp, err error) {
	// 构建查询参数
	query := &model.WarehouseListQuery{
		Page:     req.Page,
		PageSize: req.PageSize,
		Keyword:  req.Keyword,
		Status:   req.Status,
	}

	// 查询云仓列表
	result, err := l.svcCtx.WarehouseRepo.GetList(l.ctx, query)
	if err != nil {
		l.Errorf("查询云仓列表失败: %v", err)

		// 处理业务错误
		if warehouseErr := errors.GetWarehouseError(err); warehouseErr != nil {
			return &types.GetWarehouseListResp{
				BaseMsgResp: types.BaseMsgResp{
					Code: warehouseErr.Code,
					Msg:  warehouseErr.Message,
				},
			}, nil
		}

		// 系统错误
		return &types.GetWarehouseListResp{
			BaseMsgResp: types.BaseMsgResp{
				Code: constants.ErrCodeDatabaseError,
				Msg:  constants.ErrMsgDatabaseError,
			},
		}, nil
	}

	// 转换数据格式
	warehouses := make([]types.Warehouse, 0, len(result.List))
	for _, w := range result.List {
		warehouses = append(warehouses, types.Warehouse{
			Id:            w.ID,
			WarehouseCode: w.WarehouseCode,
			WarehouseName: w.WarehouseName,
			CompanyName:   w.CompanyName,
			ContactPerson: w.ContactPerson,
			ContactPhone:  w.ContactPhone,
			ContactEmail:  w.ContactEmail,
			Address:       w.Address,
			Province:      w.Province,
			City:          w.City,
			District:      w.District,
			ApiEndpoint:   w.ApiEndpoint,
			AppKey:        w.AppKey,
			CustomerID:    w.CustomerID,
			Status:        w.Status,
			CreatedAt:     w.CreatedAt.Unix(),
			UpdatedAt:     w.UpdatedAt.Unix(),
		})
	}

	return &types.GetWarehouseListResp{
		BaseMsgResp: types.BaseMsgResp{
			Code: constants.ErrCodeSuccess,
			Msg:  constants.ErrMsgSuccess,
		},
		Data: types.WarehouseListData{
			Data: warehouses,
			BaseListInfo: types.BaseListInfo{
				Total: uint64(result.Total),
			},
		},
	}, nil
}
