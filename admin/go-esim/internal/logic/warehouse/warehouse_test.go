package warehouse

import (
	"testing"

	"github.com/Wenpiner/iot-api/internal/types"
	"github.com/Wenpiner/iot-api/pkg/constants"
)

// TestCreateWarehouse_ValidateRequest 测试创建云仓的参数验证
func TestCreateWarehouse_ValidateRequest(t *testing.T) {
	logic := &CreateWarehouseLogic{}

	tests := []struct {
		name    string
		req     *types.CreateWarehouseReq
		wantErr bool
	}{
		{
			name:    "空请求",
			req:     nil,
			wantErr: true,
		},
		{
			name: "云仓编码为空",
			req: &types.CreateWarehouseReq{
				WarehouseCode: "",
				WarehouseName: "测试云仓",
			},
			wantErr: true,
		},
		{
			name: "云仓编码格式无效",
			req: &types.CreateWarehouseReq{
				WarehouseCode: "test@#$",
				WarehouseName: "测试云仓",
			},
			wantErr: true,
		},
		{
			name: "云仓名称为空",
			req: &types.CreateWarehouseReq{
				WarehouseCode: "TEST001",
				WarehouseName: "",
			},
			wantErr: true,
		},
		{
			name: "公司名称为空",
			req: &types.CreateWarehouseReq{
				WarehouseCode: "TEST001",
				WarehouseName: "测试云仓",
				CompanyName:   "",
			},
			wantErr: true,
		},
		{
			name: "联系人为空",
			req: &types.CreateWarehouseReq{
				WarehouseCode: "TEST001",
				WarehouseName: "测试云仓",
				CompanyName:   "测试公司",
				ContactPerson: "",
			},
			wantErr: true,
		},
		{
			name: "联系电话格式无效",
			req: &types.CreateWarehouseReq{
				WarehouseCode: "TEST001",
				WarehouseName: "测试云仓",
				CompanyName:   "测试公司",
				ContactPerson: "张三",
				ContactPhone:  "abc",
			},
			wantErr: true,
		},
		{
			name: "邮箱格式无效",
			req: &types.CreateWarehouseReq{
				WarehouseCode: "TEST001",
				WarehouseName: "测试云仓",
				CompanyName:   "测试公司",
				ContactPerson: "张三",
				ContactPhone:  "13800138000",
				ContactEmail:  "invalid-email",
			},
			wantErr: true,
		},
		{
			name: "地址为空",
			req: &types.CreateWarehouseReq{
				WarehouseCode: "TEST001",
				WarehouseName: "测试云仓",
				CompanyName:   "测试公司",
				ContactPerson: "张三",
				ContactPhone:  "13800138000",
				ContactEmail:  "<EMAIL>",
				Address:       "",
			},
			wantErr: true,
		},
		{
			name: "省份为空",
			req: &types.CreateWarehouseReq{
				WarehouseCode: "TEST001",
				WarehouseName: "测试云仓",
				CompanyName:   "测试公司",
				ContactPerson: "张三",
				ContactPhone:  "13800138000",
				ContactEmail:  "<EMAIL>",
				Address:       "测试地址",
				Province:      "",
			},
			wantErr: true,
		},
		{
			name: "城市为空",
			req: &types.CreateWarehouseReq{
				WarehouseCode: "TEST001",
				WarehouseName: "测试云仓",
				CompanyName:   "测试公司",
				ContactPerson: "张三",
				ContactPhone:  "13800138000",
				ContactEmail:  "<EMAIL>",
				Address:       "测试地址",
				Province:      "广东省",
				City:          "",
			},
			wantErr: true,
		},
		{
			name: "API接口地址为空",
			req: &types.CreateWarehouseReq{
				WarehouseCode: "TEST001",
				WarehouseName: "测试云仓",
				CompanyName:   "测试公司",
				ContactPerson: "张三",
				ContactPhone:  "13800138000",
				ContactEmail:  "<EMAIL>",
				Address:       "测试地址",
				Province:      "广东省",
				City:          "深圳市",
				ApiEndpoint:   "",
			},
			wantErr: true,
		},
		{
			name: "AppKey为空",
			req: &types.CreateWarehouseReq{
				WarehouseCode: "TEST001",
				WarehouseName: "测试云仓",
				CompanyName:   "测试公司",
				ContactPerson: "张三",
				ContactPhone:  "13800138000",
				ContactEmail:  "<EMAIL>",
				Address:       "测试地址",
				Province:      "广东省",
				City:          "深圳市",
				ApiEndpoint:   "https://api.example.com",
				AppKey:        "",
			},
			wantErr: true,
		},
		{
			name: "AppSecret为空",
			req: &types.CreateWarehouseReq{
				WarehouseCode: "TEST001",
				WarehouseName: "测试云仓",
				CompanyName:   "测试公司",
				ContactPerson: "张三",
				ContactPhone:  "13800138000",
				ContactEmail:  "<EMAIL>",
				Address:       "测试地址",
				Province:      "广东省",
				City:          "深圳市",
				ApiEndpoint:   "https://api.example.com",
				AppKey:        "test_app_key",
				AppSecret:     "",
			},
			wantErr: true,
		},
		{
			name: "客户ID为空",
			req: &types.CreateWarehouseReq{
				WarehouseCode: "TEST001",
				WarehouseName: "测试云仓",
				CompanyName:   "测试公司",
				ContactPerson: "张三",
				ContactPhone:  "13800138000",
				ContactEmail:  "<EMAIL>",
				Address:       "测试地址",
				Province:      "广东省",
				City:          "深圳市",
				ApiEndpoint:   "https://api.example.com",
				AppKey:        "test_app_key",
				AppSecret:     "test_app_secret",
				CustomerID:    "",
			},
			wantErr: true,
		},
		{
			name: "有效请求",
			req: &types.CreateWarehouseReq{
				WarehouseCode: "TEST001",
				WarehouseName: "测试云仓",
				CompanyName:   "测试公司",
				ContactPerson: "张三",
				ContactPhone:  "13800138000",
				ContactEmail:  "<EMAIL>",
				Address:       "测试地址",
				Province:      "广东省",
				City:          "深圳市",
				District:      "南山区",
				ApiEndpoint:   "https://api.example.com",
				AppKey:        "test_app_key",
				AppSecret:     "test_app_secret",
				CustomerID:    "test_customer",
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := logic.validateCreateRequest(tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("validateCreateRequest() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// TestUpdateWarehouse_ValidateRequest 测试更新云仓的参数验证
func TestUpdateWarehouse_ValidateRequest(t *testing.T) {
	logic := &UpdateWarehouseLogic{}

	tests := []struct {
		name    string
		req     *types.UpdateWarehouseReq
		wantErr bool
	}{
		{
			name:    "空请求",
			req:     nil,
			wantErr: true,
		},
		{
			name: "ID无效",
			req: &types.UpdateWarehouseReq{
				Id: 0,
			},
			wantErr: true,
		},
		{
			name: "联系电话格式无效",
			req: &types.UpdateWarehouseReq{
				Id:           1,
				ContactPhone: "abc",
			},
			wantErr: true,
		},
		{
			name: "邮箱格式无效",
			req: &types.UpdateWarehouseReq{
				Id:           1,
				ContactEmail: "invalid-email",
			},
			wantErr: true,
		},
		{
			name: "状态无效",
			req: &types.UpdateWarehouseReq{
				Id:     1,
				Status: 99,
			},
			wantErr: true,
		},
		{
			name: "有效请求",
			req: &types.UpdateWarehouseReq{
				Id:            1,
				WarehouseName: "更新后的云仓",
				ContactPhone:  "13800138001",
				ContactEmail:  "<EMAIL>",
				Status:        constants.WarehouseStatusNormal,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := logic.validateUpdateRequest(tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("validateUpdateRequest() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
