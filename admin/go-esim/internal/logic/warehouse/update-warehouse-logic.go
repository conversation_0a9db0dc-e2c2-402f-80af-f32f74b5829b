package warehouse

import (
	"context"

	"github.com/Wenpiner/iot-api/internal/model"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/Wenpiner/iot-api/internal/types"
	"github.com/Wenpiner/iot-api/pkg/constants"
	"github.com/Wenpiner/iot-api/pkg/errors"
	"github.com/Wenpiner/iot-api/pkg/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateWarehouseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateWarehouseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateWarehouseLogic {
	return &UpdateWarehouseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx}
}

func (l *UpdateWarehouseLogic) UpdateWarehouse(req *types.UpdateWarehouseReq) (resp *types.UpdateWarehouseResp, err error) {
	// 参数验证
	if err := l.validateUpdateRequest(req); err != nil {
		l.Errorf("更新云仓参数验证失败: %v", err)
		return &types.UpdateWarehouseResp{
			BaseMsgResp: types.BaseMsgResp{
				Code: constants.ErrCodeInvalidParam,
				Msg:  err.Error(),
			},
		}, nil
	}

	// 构建更新模型
	warehouse := &model.Warehouse{
		ID:            req.Id,
		WarehouseName: req.WarehouseName,
		CompanyName:   req.CompanyName,
		ContactPerson: req.ContactPerson,
		ContactPhone:  req.ContactPhone,
		ContactEmail:  req.ContactEmail,
		Address:       req.Address,
		Province:      req.Province,
		City:          req.City,
		District:      req.District,
		ApiEndpoint:   req.ApiEndpoint,
		AppKey:        req.AppKey,
		AppSecret:     req.AppSecret,
		CustomerID:    req.CustomerID,
		Status:        req.Status,
	}

	// 更新云仓
	if err := l.svcCtx.WarehouseRepo.Update(l.ctx, warehouse); err != nil {
		l.Errorf("更新云仓失败: %v", err)

		// 处理业务错误
		if warehouseErr := errors.GetWarehouseError(err); warehouseErr != nil {
			return &types.UpdateWarehouseResp{
				BaseMsgResp: types.BaseMsgResp{
					Code: warehouseErr.Code,
					Msg:  warehouseErr.Message,
				},
			}, nil
		}

		// 系统错误
		return &types.UpdateWarehouseResp{
			BaseMsgResp: types.BaseMsgResp{
				Code: constants.ErrCodeDatabaseError,
				Msg:  constants.ErrMsgDatabaseError,
			},
		}, nil
	}

	l.Infof("更新云仓成功: ID=%d", req.Id)

	return &types.UpdateWarehouseResp{
		BaseMsgResp: types.BaseMsgResp{
			Code: constants.ErrCodeSuccess,
			Msg:  constants.ErrMsgSuccess,
		},
	}, nil
}

// validateUpdateRequest 验证更新请求参数
func (l *UpdateWarehouseLogic) validateUpdateRequest(req *types.UpdateWarehouseReq) error {
	if req == nil {
		return errors.ErrInvalidParam.WithDetail("请求参数不能为空")
	}
	if req.Id <= 0 {
		return errors.ErrInvalidParam.WithDetail("云仓ID无效")
	}

	// 验证可选字段格式
	if req.ContactPhone != "" && !utils.ValidatePhone(req.ContactPhone) {
		return errors.ErrInvalidParam.WithDetail("联系电话格式无效")
	}
	if req.ContactEmail != "" && !utils.ValidateEmail(req.ContactEmail) {
		return errors.ErrInvalidParam.WithDetail("邮箱格式无效")
	}
	if req.Status > 0 && req.Status != constants.WarehouseStatusNormal && req.Status != constants.WarehouseStatusDisabled {
		return errors.ErrInvalidParam.WithDetail("云仓状态无效")
	}

	return nil
}
