syntax = "v1"
import "base.api"

type TaskListReq {
    Page PageInfo `json:"page"`
    Type *int32 `json:"type,optional"`
    Status *int32 `json:"status,optional"`
}

type TaskListItem {
    ID int64 `json:"id"`
    Type int32 `json:"type"`
    OperatorId string `json:"operatorId"`
    Status int32 `json:"status"`
    SuccessRecords int64 `json:"successRecords"`
    TotalRecords int64 `json:"totalRecords"`
    FailedRecords int64 `json:"failedRecords"`
    ErrorMessage string `json:"errorMessage"`
    CreatedAt int64 `json:"createdAt"`
    UpdatedAt int64 `json:"updatedAt"`
    ExtData string `json:"extData"`
}

type TaskListInfo {
    BaseListInfo
    Data []TaskListItem `json:"data"`
}

type TaskListResp {
    BaseMsgResp
    Data TaskListInfo `json:"data"`
}

type TaskLogReq {
    TaskID int64 `json:"taskId"`
    Page PageInfo `json:"page"`
    Status *int32 `json:"status,optional"`
}

type TaskLogInfo {
    ID int64 `json:"id"`
    TaskID int64 `json:"taskId"`
    RecordId int32 `json:"recordId"`
    RecordText string `json:"recordText"`
    Status int32 `json:"status"`
    CreatedAt int64 `json:"createdAt"`
    ErrorText string `json:"errorText"`
}

type TaskLogListInfo {
    BaseListInfo
    Data []TaskLogInfo `json:"data"`
}

type TaskLogResp {
    BaseMsgResp
    Data TaskLogListInfo `json:"data"`
}


@server(
	group: task
    prefix: /esim-task
    middleware: Authority
    jwt: Auth
)
service Esim {
    // 获取导入记录
    @handler getTaskList
    post /list(TaskListReq) returns (TaskListResp)

    // 获取任务日志
    @handler getTaskLog
    post /log(TaskLogReq) returns (TaskLogResp)
}