syntax = "v1"

// The basic response with data | 基础带数据信息
type BaseDataInfo {
    // Error code | 错误代码
    Code int    `json:"code"`

    // Message | 提示信息
    Msg  string `json:"msg"`

    // Data | 数据
    Data string `json:"data,omitempty"`
}

// The basic response with data | 基础带数据信息
type BaseListInfo {
    // The total number of data | 数据总数
    Total uint64 `json:"total"`

    // Data | 数据
    Data string `json:"data,omitempty"`
}

// The basic response without data | 基础不带数据信息
type BaseMsgResp {
    // Error code | 错误代码
    Code int    `json:"code"`

    // Message | 提示信息
    Msg  string `json:"msg"`
}

// The page request parameters | 列表请求参数
type PageInfo {
    // Page number | 第几页
    Page   uint64    `json:"page" validate:"required,number,gt=0"`

    // Page size | 单页数据行数
    PageSize  uint64    `json:"pageSize" validate:"required,number,lt=100000"`
}

// Basic ID request | 基础ID参数请求
type IDReq {
    // ID
    // Required: true
    Id  uint64 `json:"id" validate:"number"`
}

// Basic IDs request | 基础ID数组参数请求
type IDsReq {
    // IDs
    // Required: true
    Ids  []uint64 `json:"ids"`
}


// Basic ID request | 基础ID地址参数请求
type IDPathReq {
    // ID
    // Required: true
    Id  uint64 `path:"id"`
}

// Basic ID request (int32) | 基础ID参数请求 (int32)
type IDInt32Req {
    // ID
    // Required: true
    Id  int32 `json:"id" validate:"number"`
}

// Basic IDs request (int32) | 基础ID数组参数请求 (int32)
type IDsInt32Req {
    // IDs
    // Required: true
    Ids  []int32 `json:"ids"`
}

// Basic ID request (int32) | 基础ID地址参数请求 (int32)
type IDInt32PathReq {
    // ID
    // Required: true
    Id  int32 `path:"id"`
}

// Basic ID request (uint32) | 基础ID参数请求 (uint32)
type IDUint32Req {
    // ID
    // Required: true
    Id  uint32 `json:"id" validate:"number"`
}

// Basic IDs request (uint32) | 基础ID数组参数请求 (uint32)
type IDsUint32Req {
    // IDs
    // Required: true
    Ids  []uint32 `json:"ids"`
}

// Basic ID request (uint32) | 基础ID地址参数请求 (uint32)
type IDUint32PathReq {
    // ID
    // Required: true
    Id  uint32 `path:"id"`
}

// Basic ID request (int64) | 基础ID参数请求 (int64)
type IDInt64Req {
    // ID
    // Required: true
    Id  int64 `json:"id" validate:"number"`
}

// Basic IDs request (int64) | 基础ID数组参数请求 (int64)
type IDsInt64Req {
    // IDs
    // Required: true
    Ids  []int64 `json:"ids"`
}


// Basic ID request (int64) | 基础ID地址参数请求 (int64)
type IDInt64PathReq {
    // ID
    // Required: true
    Id  int64 `path:"id"`
}

// Basic ID request (string) | 基础ID参数请求 (string)
type IDStringReq {
    // ID
    // Required: true
    Id  string `json:"id"`
}

// Basic IDs request (string) | 基础ID数组参数请求 (string)
type IDsStringReq {
    // IDs
    // Required: true
    Ids  []string `json:"ids"`
}


// Basic ID request (string) | 基础ID地址参数请求 (string)
type IDStringPathReq {
    // ID
    // Required: true
    Id  string `path:"id"`
}

// Basic UUID request in path | 基础UUID地址参数请求
type UUIDPathReq {
    // ID
    // Required: true
    Id  string `path:"id"`
}

// Basic UUID request | 基础UUID参数请求
type UUIDReq {
    // ID
    Id string `json:"id" validate:"required,len=36"`
}

// Basic UUID array request | 基础UUID数组参数请求
type UUIDsReq {
    // Ids
    // Required: true
    Ids []string `json:"ids"`
}

// The base ID response data | 基础ID信息
type BaseIDInfo {
    // ID
    Id        *uint64    `json:"id,optional"`

    // Create date | 创建日期
    CreatedAt *int64     `json:"createdAt,optional"`

    // Update date | 更新日期
    UpdatedAt *int64     `json:"updatedAt,optional"`
}

// The base ID response data (int64) | 基础ID信息 (int64)
type BaseIDInt64Info {
    // ID
    Id        *int64    `json:"id,optional"`

    // Create date | 创建日期
    CreatedAt *int64     `json:"createdAt,optional"`

    // Update date | 更新日期
    UpdatedAt *int64     `json:"updatedAt,optional"`
}

// The base ID response data (int32) | 基础ID信息 (int32)
type BaseIDInt32Info {
    // ID
    Id        *int32    `json:"id,optional"`

    // Create date | 创建日期
    CreatedAt *int64     `json:"createdAt,optional"`

    // Update date | 更新日期
    UpdatedAt *int64     `json:"updatedAt,optional"`
}

// The base ID response data (uint32) | 基础ID信息 (uint32)
type BaseIDUint32Info {
    // ID
    Id        *uint32    `json:"id,optional"`

    // Create date | 创建日期
    CreatedAt *int64     `json:"createdAt,optional"`

    // Update date | 更新日期
    UpdatedAt *int64     `json:"updatedAt,optional"`
}

// The base UUID response data | 基础UUID信息
type BaseUUIDInfo {
    // ID
    Id        *string    `json:"id,optional"`

    // Create date | 创建日期
    CreatedAt *int64     `json:"createdAt,optional"`

    // Update date | 更新日期
    UpdatedAt *int64     `json:"updatedAt,optional"`
}

// The base ID response data (string) | 基础ID信息 (string)
type BaseIDStringInfo {
    // ID
    Id        *string    `json:"id,optional"`

    // Create date | 创建日期
    CreatedAt *int64     `json:"createdAt,optional"`

    // Update date | 更新日期
    UpdatedAt *int64     `json:"updatedAt,optional"`
}

type FileInfo {
    // File name | 文件名
    Name string `json:"name"`

    // URL | 文件URL
    URL string `json:"url"`
}
