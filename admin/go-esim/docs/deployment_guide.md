# 云仓管理系统部署指南

## 系统要求

### 硬件要求
- CPU: 2核心以上
- 内存: 4GB以上
- 磁盘: 50GB以上可用空间
- 网络: 稳定的网络连接

### 软件要求
- 操作系统: Linux (推荐 Ubuntu 20.04+, CentOS 7+)
- Go: 1.21+
- PostgreSQL: 14+
- Redis: 6.0+
- RabbitMQ: 3.8+ (可选)

## 环境准备

### 1. 安装PostgreSQL

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# CentOS/RHEL
sudo yum install postgresql-server postgresql-contrib
sudo postgresql-setup initdb
sudo systemctl enable postgresql
sudo systemctl start postgresql

# 创建数据库和用户
sudo -u postgres psql
CREATE DATABASE iot;
CREATE USER iot_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE iot TO iot_user;
\q
```

### 2. 安装Redis

```bash
# Ubuntu/Debian
sudo apt install redis-server

# CentOS/RHEL
sudo yum install redis
sudo systemctl enable redis
sudo systemctl start redis

# 配置Redis密码（可选）
sudo vim /etc/redis/redis.conf
# 取消注释并设置密码
requirepass your_redis_password
sudo systemctl restart redis
```

### 3. 安装RabbitMQ（可选）

```bash
# Ubuntu/Debian
sudo apt install rabbitmq-server

# CentOS/RHEL
sudo yum install rabbitmq-server
sudo systemctl enable rabbitmq-server
sudo systemctl start rabbitmq-server

# 启用管理插件
sudo rabbitmq-plugins enable rabbitmq_management

# 创建用户
sudo rabbitmqctl add_user admin your_password
sudo rabbitmqctl set_user_tags admin administrator
sudo rabbitmqctl set_permissions -p / admin ".*" ".*" ".*"
```

## 数据库初始化

### 1. 执行数据库脚本

```bash
# 连接数据库
psql -h localhost -U iot_user -d iot

# 执行建表脚本
\i w.sql

# 验证表结构
\dt iot.*
```

### 2. 创建索引（已包含在脚本中）

数据库脚本已包含必要的索引，包括：
- 云仓编码索引
- 入库单/出库单编码索引
- 库存查询索引
- 同步日志查询索引

## 应用部署

### 1. 编译应用

```bash
# 克隆代码
git clone <repository_url>
cd admin/go-esim

# 下载依赖
go mod tidy

# 编译
go build -o esim .
```

### 2. 配置文件

创建生产环境配置文件 `etc/esim-prod.yaml`:

```yaml
Name: Esim.api
Host: 0.0.0.0
Port: 8081
Timeout: 30000

Auth:
  AccessSecret: ${JWT_SECRET}
  AccessExpire: 259200

CROSConf:
  Address: '*'

Log:
  ServiceName: EsimApiLogger
  Encoding: json
  Stat: true
  Level: info

RedisConf:
  Host: ${REDIS_HOST}
  Pass: ${REDIS_PASSWORD}
  Db: 0

DatabaseConf:
  Type: postgres
  Host: ${DB_HOST}
  Port: ${DB_PORT}
  DBName: ${DB_NAME}
  Username: ${DB_USERNAME}
  Password: ${DB_PASSWORD}
  MaxOpenConn: 100
  SSLMode: disable
  CacheTime: 5

CasbinConf:
  ModelText: |
    [request_definition]
    r = sub, obj, act
    [policy_definition]
    p = sub, obj, act
    [role_definition]
    g = _, _
    [policy_effect]
    e = some(where (p.eft == allow))
    [matchers]
    m = r.sub == p.sub && keyMatch2(r.obj,p.obj) && r.act == p.act

RabbitMQConf:
  Host: ${RABBITMQ_HOST}
  Port: ${RABBITMQ_PORT}
  Username: ${RABBITMQ_USER}
  Password: ${RABBITMQ_PASSWORD}
  VHost: "/"

QimenConf:
  DefaultTimeout: 30
  MaxRetryCount: 3
  RetryInterval: 5
```

### 3. 环境变量

创建环境变量文件 `.env`:

```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=iot
DB_USERNAME=iot_user
DB_PASSWORD=your_db_password

# Redis配置
REDIS_HOST=localhost:6379
REDIS_PASSWORD=your_redis_password

# JWT配置
JWT_SECRET=your_jwt_secret_key_here

# RabbitMQ配置
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=admin
RABBITMQ_PASSWORD=your_rabbitmq_password
```

### 4. 创建系统服务

创建systemd服务文件 `/etc/systemd/system/esim.service`:

```ini
[Unit]
Description=Esim Warehouse Management API
After=network.target postgresql.service redis.service

[Service]
Type=simple
User=esim
Group=esim
WorkingDirectory=/opt/esim
ExecStart=/opt/esim/esim -f /opt/esim/etc/esim-prod.yaml
EnvironmentFile=/opt/esim/.env
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

### 5. 部署步骤

```bash
# 创建用户
sudo useradd -r -s /bin/false esim

# 创建目录
sudo mkdir -p /opt/esim
sudo mkdir -p /opt/esim/etc
sudo mkdir -p /opt/esim/logs

# 复制文件
sudo cp esim /opt/esim/
sudo cp etc/esim-prod.yaml /opt/esim/etc/
sudo cp .env /opt/esim/

# 设置权限
sudo chown -R esim:esim /opt/esim
sudo chmod +x /opt/esim/esim

# 启动服务
sudo systemctl daemon-reload
sudo systemctl enable esim
sudo systemctl start esim

# 检查状态
sudo systemctl status esim
```

## 监控和日志

### 1. 日志管理

```bash
# 查看应用日志
sudo journalctl -u esim -f

# 查看错误日志
sudo journalctl -u esim -p err

# 日志轮转配置
sudo vim /etc/logrotate.d/esim
```

### 2. 健康检查

创建健康检查脚本 `/opt/esim/health_check.sh`:

```bash
#!/bin/bash

# 检查服务状态
if ! systemctl is-active --quiet esim; then
    echo "Service is not running"
    exit 1
fi

# 检查API响应
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8081/health)
if [ "$response" != "200" ]; then
    echo "API health check failed"
    exit 1
fi

echo "Health check passed"
exit 0
```

### 3. 性能监控

推荐使用以下工具进行监控：
- Prometheus + Grafana
- ELK Stack (Elasticsearch + Logstash + Kibana)
- 阿里云/腾讯云监控服务

## 安全配置

### 1. 防火墙配置

```bash
# 开放必要端口
sudo ufw allow 8081/tcp  # API端口
sudo ufw allow 22/tcp    # SSH端口
sudo ufw enable
```

### 2. SSL/TLS配置

建议使用Nginx作为反向代理，配置SSL证书：

```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;

    location / {
        proxy_pass http://localhost:8081;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 3. 数据库安全

```bash
# 修改PostgreSQL配置
sudo vim /etc/postgresql/14/main/postgresql.conf
# 设置监听地址
listen_addresses = 'localhost'

sudo vim /etc/postgresql/14/main/pg_hba.conf
# 配置访问控制
local   iot             iot_user                                md5
host    iot             iot_user        127.0.0.1/32            md5
```

## 备份和恢复

### 1. 数据库备份

```bash
# 创建备份脚本
#!/bin/bash
BACKUP_DIR="/opt/backups"
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump -h localhost -U iot_user -d iot > $BACKUP_DIR/iot_backup_$DATE.sql

# 设置定时任务
crontab -e
# 每天凌晨2点备份
0 2 * * * /opt/scripts/backup_db.sh
```

### 2. 应用备份

```bash
# 备份应用文件
tar -czf /opt/backups/esim_backup_$(date +%Y%m%d).tar.gz /opt/esim
```

## 故障排除

### 1. 常见问题

**服务无法启动**
```bash
# 检查配置文件
sudo -u esim /opt/esim/esim -f /opt/esim/etc/esim-prod.yaml -test

# 检查端口占用
sudo netstat -tlnp | grep 8081

# 检查权限
ls -la /opt/esim/
```

**数据库连接失败**
```bash
# 测试数据库连接
psql -h localhost -U iot_user -d iot

# 检查PostgreSQL状态
sudo systemctl status postgresql
```

**Redis连接失败**
```bash
# 测试Redis连接
redis-cli -h localhost -p 6379 ping

# 检查Redis状态
sudo systemctl status redis
```

### 2. 性能调优

**数据库优化**
```sql
-- 分析查询性能
EXPLAIN ANALYZE SELECT * FROM iot.warehouses WHERE status = 1;

-- 更新统计信息
ANALYZE;

-- 重建索引
REINDEX DATABASE iot;
```

**应用优化**
- 调整数据库连接池大小
- 配置Redis缓存策略
- 优化日志级别
- 调整超时时间

## 升级指南

### 1. 应用升级

```bash
# 停止服务
sudo systemctl stop esim

# 备份当前版本
cp /opt/esim/esim /opt/esim/esim.backup

# 部署新版本
cp esim /opt/esim/

# 更新配置（如需要）
# 执行数据库迁移（如需要）

# 启动服务
sudo systemctl start esim

# 验证升级
curl http://localhost:8081/health
```

### 2. 数据库升级

```bash
# 备份数据库
pg_dump -h localhost -U iot_user -d iot > backup_before_upgrade.sql

# 执行升级脚本
psql -h localhost -U iot_user -d iot -f upgrade_script.sql

# 验证升级
psql -h localhost -U iot_user -d iot -c "\dt iot.*"
```

通过以上部署指南，您可以成功部署和运维云仓管理系统。建议在生产环境中根据实际情况调整配置参数。
