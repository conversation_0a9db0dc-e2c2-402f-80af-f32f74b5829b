# 云仓管理系统API使用指南

## 概述

本文档介绍云仓管理系统的API接口使用方法，包括云仓基础信息管理、入库管理、出库管理、库存管理和奇门API同步等功能。

## 基础配置

### 环境变量

```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=iot
DB_USERNAME=postgres
DB_PASSWORD=your_password

# Redis配置
REDIS_HOST=localhost:6379
REDIS_PASSWORD=your_redis_password

# JWT配置
JWT_SECRET=your_jwt_secret

# RabbitMQ配置
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest
```

### 启动服务

```bash
# 编译
go build -o esim .

# 运行
./esim -f etc/esim.yaml
```

## API接口

### 1. 云仓基础信息管理

#### 1.1 创建云仓

**接口地址**: `POST /warehouse/create`

**请求参数**:
```json
{
  "warehouse_code": "WH001",
  "warehouse_name": "深圳云仓",
  "company_name": "深圳仓储有限公司",
  "contact_person": "张三",
  "contact_phone": "13800138000",
  "contact_email": "<EMAIL>",
  "address": "深圳市南山区科技园",
  "province": "广东省",
  "city": "深圳市",
  "district": "南山区",
  "api_endpoint": "https://api.warehouse.com",
  "app_key": "your_app_key",
  "app_secret": "your_app_secret",
  "customer_id": "your_customer_id"
}
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "id": 1
  }
}
```

#### 1.2 获取云仓列表

**接口地址**: `GET /warehouse/list`

**请求参数**:
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20
- `keyword`: 搜索关键词（可选）
- `status`: 状态筛选（可选）

**响应示例**:
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "list": [
      {
        "id": 1,
        "warehouse_code": "WH001",
        "warehouse_name": "深圳云仓",
        "company_name": "深圳仓储有限公司",
        "contact_person": "张三",
        "contact_phone": "13800138000",
        "contact_email": "<EMAIL>",
        "address": "深圳市南山区科技园",
        "province": "广东省",
        "city": "深圳市",
        "district": "南山区",
        "api_endpoint": "https://api.warehouse.com",
        "app_key": "your_app_key",
        "customer_id": "your_customer_id",
        "status": 1,
        "created_at": 1640995200,
        "updated_at": 1640995200
      }
    ],
    "page": {
      "page": 1,
      "pageSize": 20
    }
  }
}
```

#### 1.3 获取云仓详情

**接口地址**: `GET /warehouse/detail/{id}`

**响应示例**:
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "id": 1,
    "warehouse_code": "WH001",
    "warehouse_name": "深圳云仓",
    "company_name": "深圳仓储有限公司",
    "contact_person": "张三",
    "contact_phone": "13800138000",
    "contact_email": "<EMAIL>",
    "address": "深圳市南山区科技园",
    "province": "广东省",
    "city": "深圳市",
    "district": "南山区",
    "api_endpoint": "https://api.warehouse.com",
    "app_key": "your_app_key",
    "customer_id": "your_customer_id",
    "status": 1,
    "created_at": 1640995200,
    "updated_at": 1640995200
  }
}
```

#### 1.4 更新云仓

**接口地址**: `PUT /warehouse/update`

**请求参数**:
```json
{
  "id": 1,
  "warehouse_name": "深圳云仓（更新）",
  "contact_phone": "13800138001",
  "status": 1
}
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "操作成功"
}
```

### 2. 入库管理

#### 2.1 创建入库单

**接口地址**: `POST /warehouse/entry/order/create`

**请求参数**:
```json
{
  "warehouse_code": "WH001",
  "order_type": "CGRK",
  "purchase_order_code": "PO20240101001",
  "expect_start_time": "2024-01-01 09:00:00",
  "expect_end_time": "2024-01-01 18:00:00",
  "logistics_code": "SF",
  "logistics_name": "顺丰速运",
  "express_code": "SF1234567890",
  "supplier_code": "SUP001",
  "supplier_name": "供应商A",
  "operator_name": "操作员A",
  "remark": "测试入库单",
  "order_lines": [
    {
      "item_code": "ITEM001",
      "item_name": "测试商品A",
      "plan_qty": 10,
      "purchase_price": 100.00,
      "retail_price": 150.00,
      "batch_code": "BATCH001",
      "produce_code": "PROD001",
      "product_date": "2024-01-01",
      "expire_date": "2025-01-01",
      "box_number": "BOX001",
      "pallet_number": "PALLET001",
      "sn_codes": ["SN001", "SN002", "SN003"],
      "remark": "测试商品备注"
    }
  ]
}
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "id": 1,
    "entry_order_code": "RK20240101001"
  }
}
```

#### 2.2 获取入库单列表

**接口地址**: `GET /warehouse/entry/order/list`

**请求参数**:
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20
- `warehouse_code`: 云仓编码筛选（可选）
- `order_status`: 订单状态筛选（可选）
- `supplier_code`: 供应商编码筛选（可选）
- `start_time`: 开始时间筛选（可选）
- `end_time`: 结束时间筛选（可选）
- `keyword`: 关键词搜索（可选）

#### 2.3 获取入库单详情

**接口地址**: `GET /warehouse/entry/order/detail/{id}`

#### 2.4 确认入库

**接口地址**: `PUT /warehouse/entry/order/confirm`

**请求参数**:
```json
{
  "id": 1,
  "actual_arrival_time": "2024-01-01 10:30:00",
  "operator_name": "操作员A",
  "remark": "确认入库",
  "order_lines": [
    {
      "id": 1,
      "actual_qty": 10,
      "shelf_location": "A-01-01",
      "device_status": 1,
      "remark": "入库正常"
    }
  ]
}
```

#### 2.5 取消入库单

**接口地址**: `PUT /warehouse/entry/order/cancel`

**请求参数**:
```json
{
  "id": 1,
  "cancel_reason": "供应商取消发货",
  "operator_name": "操作员A"
}
```

## 错误码说明

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 0 | 操作成功 | 请求成功 |
| 10001 | 参数错误 | 请求参数格式或内容错误 |
| 10002 | 云仓不存在 | 指定的云仓不存在 |
| 10003 | 入库单不存在 | 指定的入库单不存在 |
| 10004 | 出库单不存在 | 指定的出库单不存在 |
| 10005 | 库存不存在 | 指定的库存记录不存在 |
| 10006 | 库存不足 | 可用库存数量不足 |
| 10007 | 订单状态无效 | 订单状态不允许当前操作 |
| 10008 | 奇门API错误 | 调用奇门API失败 |
| 10009 | 数据库错误 | 数据库操作失败 |
| 10010 | 并发更新冲突 | 数据已被其他用户修改 |

## 状态说明

### 云仓状态
- 1: 正常
- 2: 停用

### 入库单状态
- 1: 待入库
- 2: 部分入库
- 3: 全部入库
- 4: 异常
- 5: 取消

### 出库单状态
- 1: 待出库
- 2: 已出库
- 3: 已发货
- 4: 已收货
- 5: 异常
- 6: 取消

### 设备状态
- 1: 正常
- 2: 损坏
- 3: 缺失配件

### API同步状态
- 0: 未同步
- 1: 已同步
- 2: 同步失败

## 注意事项

1. 所有时间字段支持以下格式：
   - `2006-01-02 15:04:05`
   - `2006-01-02T15:04:05Z`
   - `2006-01-02T15:04:05+08:00`
   - `2006-01-02`

2. 云仓编码必须全局唯一，只允许字母、数字、下划线、中划线

3. 商品编码建议使用设备序列号或唯一标识

4. 入库单和出库单的状态转换有严格的业务规则，不能随意跳转

5. 奇门API同步是异步操作，可通过同步日志查看详细状态

6. 建议在生产环境中配置适当的数据库连接池和Redis缓存策略
