package errors

import (
	"fmt"
	"github.com/Wenpiner/iot-api/pkg/constants"
)

// WarehouseError 云仓业务错误
type WarehouseError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Detail  string `json:"detail,omitempty"`
}

func (e *WarehouseError) Error() string {
	if e.Detail != "" {
		return fmt.Sprintf("code: %d, message: %s, detail: %s", e.Code, e.Message, e.Detail)
	}
	return fmt.Sprintf("code: %d, message: %s", e.Code, e.Message)
}

// NewWarehouseError 创建云仓业务错误
func NewWarehouseError(code int, message string, detail ...string) *WarehouseError {
	err := &WarehouseError{
		Code:    code,
		Message: message,
	}
	if len(detail) > 0 {
		err.Detail = detail[0]
	}
	return err
}

// 预定义错误
var (
	ErrInvalidParam         = NewWarehouseError(constants.ErrCodeInvalidParam, constants.ErrMsgInvalidParam)
	ErrWarehouseNotFound    = NewWarehouseError(constants.ErrCodeWarehouseNotFound, constants.ErrMsgWarehouseNotFound)
	ErrEntryOrderNotFound   = NewWarehouseError(constants.ErrCodeEntryOrderNotFound, constants.ErrMsgEntryOrderNotFound)
	ErrDeliveryOrderNotFound = NewWarehouseError(constants.ErrCodeDeliveryOrderNotFound, constants.ErrMsgDeliveryOrderNotFound)
	ErrInventoryNotFound    = NewWarehouseError(constants.ErrCodeInventoryNotFound, constants.ErrMsgInventoryNotFound)
	ErrInsufficientStock    = NewWarehouseError(constants.ErrCodeInsufficientStock, constants.ErrMsgInsufficientStock)
	ErrOrderStatusInvalid   = NewWarehouseError(constants.ErrCodeOrderStatusInvalid, constants.ErrMsgOrderStatusInvalid)
	ErrQimenApiError        = NewWarehouseError(constants.ErrCodeQimenApiError, constants.ErrMsgQimenApiError)
	ErrDatabaseError        = NewWarehouseError(constants.ErrCodeDatabaseError, constants.ErrMsgDatabaseError)
	ErrConcurrentUpdate     = NewWarehouseError(constants.ErrCodeConcurrentUpdate, constants.ErrMsgConcurrentUpdate)
)

// WithDetail 添加错误详情
func (e *WarehouseError) WithDetail(detail string) *WarehouseError {
	return &WarehouseError{
		Code:    e.Code,
		Message: e.Message,
		Detail:  detail,
	}
}

// IsWarehouseError 判断是否为云仓业务错误
func IsWarehouseError(err error) bool {
	_, ok := err.(*WarehouseError)
	return ok
}

// GetWarehouseError 获取云仓业务错误
func GetWarehouseError(err error) *WarehouseError {
	if warehouseErr, ok := err.(*WarehouseError); ok {
		return warehouseErr
	}
	return nil
}
