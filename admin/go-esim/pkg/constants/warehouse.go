package constants

// 云仓状态常量
const (
	WarehouseStatusNormal   = 1 // 正常
	WarehouseStatusDisabled = 2 // 停用
)

// 入库单状态常量
const (
	EntryOrderStatusPending    = 1 // 待入库
	EntryOrderStatusPartial    = 2 // 部分入库
	EntryOrderStatusCompleted  = 3 // 全部入库
	EntryOrderStatusException  = 4 // 异常
	EntryOrderStatusCancelled  = 5 // 取消
)

// 出库单状态常量
const (
	DeliveryOrderStatusPending   = 1 // 待出库
	DeliveryOrderStatusDelivered = 2 // 已出库
	DeliveryOrderStatusShipped   = 3 // 已发货
	DeliveryOrderStatusReceived  = 4 // 已收货
	DeliveryOrderStatusException = 5 // 异常
	DeliveryOrderStatusCancelled = 6 // 取消
)

// 库存状态常量
const (
	InventoryStatusInStock    = 1 // 在库
	InventoryStatusPreOut     = 2 // 预出库
	InventoryStatusOut        = 3 // 已出库
	InventoryStatusDamaged    = 4 // 损坏
	InventoryStatusLost       = 5 // 丢失
)

// 设备状态常量
const (
	DeviceStatusNormal      = 1 // 正常
	DeviceStatusDamaged     = 2 // 损坏
	DeviceStatusMissingPart = 3 // 缺失配件
)

// 收货人类型常量
const (
	RecipientTypePartner = 1 // 合伙人
	RecipientTypeAgent   = 2 // 代理商
	RecipientTypeUser    = 3 // 用户
)

// API同步状态常量
const (
	ApiSyncStatusNotSynced = 0 // 未同步
	ApiSyncStatusSynced    = 1 // 已同步
	ApiSyncStatusFailed    = 2 // 同步失败
)

// 奇门同步类型常量
const (
	QimenSyncTypeEntry     = 1 // 入库单
	QimenSyncTypeDelivery  = 2 // 出库单
	QimenSyncTypeInventory = 3 // 库存查询
	QimenSyncTypeStock     = 4 // 库存同步
)

// 奇门同步方向常量
const (
	QimenSyncDirectionPush = 1 // 推送到云仓
	QimenSyncDirectionPull = 2 // 从云仓拉取
)

// 奇门同步状态常量
const (
	QimenSyncStatusSuccess = 1 // 成功
	QimenSyncStatusFailed  = 2 // 失败
	QimenSyncStatusPartial = 3 // 部分成功
)

// 库存操作类型常量
const (
	InventoryOperationTypeInbound  = 1 // 入库
	InventoryOperationTypeOutbound = 2 // 出库
	InventoryOperationTypeCheck    = 3 // 盘点
	InventoryOperationTypeTransfer = 4 // 调拨
	InventoryOperationTypeDamage   = 5 // 报损
	InventoryOperationTypeOverflow = 6 // 报溢
)

// 库存调整类型常量
const (
	InventoryAdjustTypeIncrease = 1 // 增加
	InventoryAdjustTypeDecrease = 2 // 减少
)

// 盘点类型常量
const (
	InventoryCheckTypeFull   = 1 // 全盘
	InventoryCheckTypeSpot   = 2 // 抽盘
	InventoryCheckTypeCycle  = 3 // 循环盘点
)

// 盘点状态常量
const (
	InventoryCheckStatusInProgress = 1 // 进行中
	InventoryCheckStatusCompleted  = 2 // 已完成
)

// 业务类型常量
const (
	OrderTypeCGRK = "CGRK" // 采购入库
	OrderTypeSCRK = "SCRK" // 生产入库
	OrderTypeDBRK = "DBRK" // 调拨入库
	OrderTypeJYCK = "JYCK" // 交易出库
	OrderTypeDBCK = "DBCK" // 调拨出库
)

// 库存类型常量
const (
	InventoryTypeZP = "ZP" // 正品
	InventoryTypeCC = "CC" // 残次
	InventoryTypeJS = "JS" // 机损
	InventoryTypeXS = "XS" // 箱损
)

// 默认值常量
const (
	DefaultOwnerCode = "NIUYI" // 默认货主编码
	DefaultUnit      = "台"     // 默认单位
)

// 奇门API方法名常量
const (
	QimenApiEntryOrderCreate   = "taobao.qimen.entryorder.create"   // 创建入库单
	QimenApiEntryOrderConfirm  = "taobao.qimen.entryorder.confirm"  // 确认入库
	QimenApiDeliveryOrderCreate = "taobao.qimen.deliveryorder.create" // 创建出库单
	QimenApiDeliveryOrderConfirm = "taobao.qimen.deliveryorder.confirm" // 确认出库
	QimenApiInventoryQuery     = "taobao.qimen.inventory.query"     // 库存查询
	QimenApiInventorySync      = "taobao.qimen.inventory.sync"      // 库存同步
)

// 错误码常量
const (
	ErrCodeSuccess              = 0     // 成功
	ErrCodeInvalidParam         = 10001 // 参数错误
	ErrCodeWarehouseNotFound    = 10002 // 云仓不存在
	ErrCodeEntryOrderNotFound   = 10003 // 入库单不存在
	ErrCodeDeliveryOrderNotFound = 10004 // 出库单不存在
	ErrCodeInventoryNotFound    = 10005 // 库存不存在
	ErrCodeInsufficientStock    = 10006 // 库存不足
	ErrCodeOrderStatusInvalid   = 10007 // 订单状态无效
	ErrCodeQimenApiError        = 10008 // 奇门API错误
	ErrCodeDatabaseError        = 10009 // 数据库错误
	ErrCodeConcurrentUpdate     = 10010 // 并发更新冲突
)

// 错误消息常量
const (
	ErrMsgSuccess              = "操作成功"
	ErrMsgInvalidParam         = "参数错误"
	ErrMsgWarehouseNotFound    = "云仓不存在"
	ErrMsgEntryOrderNotFound   = "入库单不存在"
	ErrMsgDeliveryOrderNotFound = "出库单不存在"
	ErrMsgInventoryNotFound    = "库存不存在"
	ErrMsgInsufficientStock    = "库存不足"
	ErrMsgOrderStatusInvalid   = "订单状态无效"
	ErrMsgQimenApiError        = "奇门API调用失败"
	ErrMsgDatabaseError        = "数据库操作失败"
	ErrMsgConcurrentUpdate     = "数据已被其他用户修改，请刷新后重试"
)
