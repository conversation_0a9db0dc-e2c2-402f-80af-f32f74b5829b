package utils

import (
	"crypto/md5"
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"time"
)

// GenerateOrderCode 生成订单号
// prefix: 前缀 (如 RK-入库, CK-出库)
func GenerateOrderCode(prefix string) string {
	now := time.Now()
	timestamp := now.Format("20060102150405")
	random := rand.Intn(9999)
	return fmt.Sprintf("%s%s%04d", prefix, timestamp, random)
}

// GenerateEntryOrderCode 生成入库单号
func GenerateEntryOrderCode() string {
	return GenerateOrderCode("RK")
}

// GenerateDeliveryOrderCode 生成出库单号
func GenerateDeliveryOrderCode() string {
	return GenerateOrderCode("CK")
}

// GenerateOrderLineNo 生成订单行号
func GenerateOrderLineNo(orderCode string, lineIndex int) string {
	return fmt.Sprintf("%s-%03d", orderCode, lineIndex)
}

// GenerateOutBizCode 生成外部业务编码（用于去重）
func GenerateOutBizCode(orderCode, itemCode string) string {
	source := fmt.Sprintf("%s_%s_%d", orderCode, itemCode, time.Now().UnixNano())
	hash := md5.Sum([]byte(source))
	return fmt.Sprintf("%x", hash)[:16]
}

// FormatTimestamp 格式化时间戳为字符串
func FormatTimestamp(timestamp int64) string {
	if timestamp == 0 {
		return ""
	}
	return time.Unix(timestamp, 0).Format("2006-01-02 15:04:05")
}

// ParseTimestamp 解析时间字符串为时间戳
func ParseTimestamp(timeStr string) (int64, error) {
	if timeStr == "" {
		return 0, nil
	}
	
	// 尝试多种时间格式
	formats := []string{
		"2006-01-02 15:04:05",
		"2006-01-02T15:04:05Z",
		"2006-01-02T15:04:05+08:00",
		"2006-01-02",
	}
	
	for _, format := range formats {
		if t, err := time.Parse(format, timeStr); err == nil {
			return t.Unix(), nil
		}
	}
	
	return 0, fmt.Errorf("无法解析时间格式: %s", timeStr)
}

// ValidateWarehouseCode 验证云仓编码格式
func ValidateWarehouseCode(code string) bool {
	if len(code) < 2 || len(code) > 50 {
		return false
	}
	// 只允许字母、数字、下划线、中划线
	for _, r := range code {
		if !((r >= 'A' && r <= 'Z') || (r >= 'a' && r <= 'z') || 
			 (r >= '0' && r <= '9') || r == '_' || r == '-') {
			return false
		}
	}
	return true
}

// ValidateItemCode 验证商品编码格式
func ValidateItemCode(code string) bool {
	if len(code) < 1 || len(code) > 100 {
		return false
	}
	return strings.TrimSpace(code) != ""
}

// ValidatePhone 验证电话号码格式
func ValidatePhone(phone string) bool {
	if len(phone) < 7 || len(phone) > 20 {
		return false
	}
	// 简单验证：只允许数字、中划线、空格、括号、加号
	for _, r := range phone {
		if !((r >= '0' && r <= '9') || r == '-' || r == ' ' || 
			 r == '(' || r == ')' || r == '+') {
			return false
		}
	}
	return true
}

// ValidateEmail 验证邮箱格式
func ValidateEmail(email string) bool {
	if email == "" {
		return true // 可选字段
	}
	return strings.Contains(email, "@") && strings.Contains(email, ".")
}

// CalculateRetryDelay 计算重试延迟时间（指数退避）
func CalculateRetryDelay(retryCount int, baseDelay time.Duration) time.Duration {
	if retryCount <= 0 {
		return baseDelay
	}
	// 指数退避：baseDelay * 2^retryCount，最大不超过30分钟
	delay := baseDelay * time.Duration(1<<uint(retryCount))
	maxDelay := 30 * time.Minute
	if delay > maxDelay {
		delay = maxDelay
	}
	return delay
}

// SafeStringToInt64 安全地将字符串转换为int64
func SafeStringToInt64(s string) int64 {
	if s == "" {
		return 0
	}
	if val, err := strconv.ParseInt(s, 10, 64); err == nil {
		return val
	}
	return 0
}

// SafeStringToInt32 安全地将字符串转换为int32
func SafeStringToInt32(s string) int32 {
	if s == "" {
		return 0
	}
	if val, err := strconv.ParseInt(s, 10, 32); err == nil {
		return int32(val)
	}
	return 0
}

// SafeStringToFloat64 安全地将字符串转换为float64
func SafeStringToFloat64(s string) float64 {
	if s == "" {
		return 0
	}
	if val, err := strconv.ParseFloat(s, 64); err == nil {
		return val
	}
	return 0
}

// TruncateString 截断字符串到指定长度
func TruncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen]
}

// IsValidOrderStatus 验证订单状态是否有效
func IsValidOrderStatus(orderType string, status int32) bool {
	switch orderType {
	case "entry":
		return status >= 1 && status <= 5
	case "delivery":
		return status >= 1 && status <= 6
	default:
		return false
	}
}

// CanTransitionOrderStatus 检查订单状态是否可以转换
func CanTransitionOrderStatus(orderType string, fromStatus, toStatus int32) bool {
	if !IsValidOrderStatus(orderType, fromStatus) || !IsValidOrderStatus(orderType, toStatus) {
		return false
	}
	
	switch orderType {
	case "entry":
		// 入库单状态转换规则
		switch fromStatus {
		case 1: // 待入库 -> 部分入库、全部入库、异常、取消
			return toStatus == 2 || toStatus == 3 || toStatus == 4 || toStatus == 5
		case 2: // 部分入库 -> 全部入库、异常
			return toStatus == 3 || toStatus == 4
		case 3, 4, 5: // 终态，不能转换
			return false
		}
	case "delivery":
		// 出库单状态转换规则
		switch fromStatus {
		case 1: // 待出库 -> 已出库、异常、取消
			return toStatus == 2 || toStatus == 5 || toStatus == 6
		case 2: // 已出库 -> 已发货、异常
			return toStatus == 3 || toStatus == 5
		case 3: // 已发货 -> 已收货、异常
			return toStatus == 4 || toStatus == 5
		case 4, 5, 6: // 终态，不能转换
			return false
		}
	}
	
	return false
}

// GetCurrentTimestamp 获取当前时间戳
func GetCurrentTimestamp() int64 {
	return time.Now().Unix()
}

// GetCurrentTimeString 获取当前时间字符串
func GetCurrentTimeString() string {
	return time.Now().Format("2006-01-02 15:04:05")
}
