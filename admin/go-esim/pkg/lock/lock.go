package lock

import (
	"context"
	"crypto/rand"
	"errors"
	"fmt"
	"os"
	"time"

	"github.com/redis/go-redis/v9"
)

// 错误定义
var (
	ErrLockNotAcquired     = errors.New("锁获取失败")
	ErrLockNotFound        = errors.New("锁不存在")
	ErrLockAlreadyReleased = errors.New("锁已释放")
	ErrUnsupportedBackend  = errors.New("不支持的后端类型")
	ErrInvalidConfig       = errors.New("无效的配置")
	ErrWatchdogStopped     = errors.New("看门狗已停止")
	ErrLockExpired         = errors.New("锁已过期")
	ErrAcquireTimeout      = errors.New("获取锁超时")
)

// lockOptions 内部选项结构
type lockOptions struct {
	acquireTimeout   time.Duration
	lockTTL          time.Duration
	enableWatchdog   bool
	watchdogInterval time.Duration
	retryInterval    time.Duration
	value            string
}

// rwLockOptions 读写锁内部选项结构
type rwLockOptions struct {
	acquireTimeout   time.Duration
	lockTTL          time.Duration
	enableWatchdog   bool
	watchdogInterval time.Duration
	retryInterval    time.Duration
	value            string
}

// fairLockOptions 公平锁内部选项结构
type fairLockOptions struct {
	acquireTimeout   time.Duration
	lockTTL          time.Duration
	enableWatchdog   bool
	watchdogInterval time.Duration
	retryInterval    time.Duration
	queueTTL         time.Duration
	value            string
}

// Option 基本锁选项函数
type Option func(*lockOptions)

// RWOption 读写锁选项函数
type RWOption func(*rwLockOptions)

// FairOption 公平锁选项函数
type FairOption func(*fairLockOptions)

// 基本锁选项函数
func WithAcquireTimeout(timeout time.Duration) Option {
	return func(o *lockOptions) {
		o.acquireTimeout = timeout
	}
}

func WithLockTTL(ttl time.Duration) Option {
	return func(o *lockOptions) {
		o.lockTTL = ttl
	}
}

func WithWatchdog(enable bool) Option {
	return func(o *lockOptions) {
		o.enableWatchdog = enable
	}
}

func WithWatchdogInterval(interval time.Duration) Option {
	return func(o *lockOptions) {
		o.watchdogInterval = interval
	}
}

func WithRetryInterval(interval time.Duration) Option {
	return func(o *lockOptions) {
		o.retryInterval = interval
	}
}

func WithValue(value string) Option {
	return func(o *lockOptions) {
		o.value = value
	}
}

// 读写锁选项函数
func WithRWAcquireTimeout(timeout time.Duration) RWOption {
	return func(o *rwLockOptions) {
		o.acquireTimeout = timeout
	}
}

func WithRWLockTTL(ttl time.Duration) RWOption {
	return func(o *rwLockOptions) {
		o.lockTTL = ttl
	}
}

func WithRWWatchdog(enable bool) RWOption {
	return func(o *rwLockOptions) {
		o.enableWatchdog = enable
	}
}

func WithRWWatchdogInterval(interval time.Duration) RWOption {
	return func(o *rwLockOptions) {
		o.watchdogInterval = interval
	}
}

func WithRWRetryInterval(interval time.Duration) RWOption {
	return func(o *rwLockOptions) {
		o.retryInterval = interval
	}
}

func WithRWValue(value string) RWOption {
	return func(o *rwLockOptions) {
		o.value = value
	}
}

// 公平锁选项函数
// WithFairAcquireTimeout 设置公平锁获取锁的超时时间
func WithFairAcquireTimeout(timeout time.Duration) FairOption {
	return func(o *fairLockOptions) {
		o.acquireTimeout = timeout
	}
}

// WithFairLockTTL 设置公平锁的锁TTL
func WithFairLockTTL(ttl time.Duration) FairOption {
	return func(o *fairLockOptions) {
		o.lockTTL = ttl
	}
}

// WithFairWatchdog 设置公平锁的看门狗
func WithFairWatchdog(enable bool) FairOption {
	return func(o *fairLockOptions) {
		o.enableWatchdog = enable
	}
}

// WithFairWatchdogInterval 设置公平锁的看门狗间隔
func WithFairWatchdogInterval(interval time.Duration) FairOption {
	return func(o *fairLockOptions) {
		o.watchdogInterval = interval
	}
}

// WithFairRetryInterval 设置公平锁的重试间隔
func WithFairRetryInterval(interval time.Duration) FairOption {
	return func(o *fairLockOptions) {
		o.retryInterval = interval
	}
}

// WithFairQueueTTL 设置公平锁的队列TTL
func WithFairQueueTTL(ttl time.Duration) FairOption {
	return func(o *fairLockOptions) {
		o.queueTTL = ttl
	}
}

// WithFairValue 设置公平锁的值
func WithFairValue(value string) FairOption {
	return func(o *fairLockOptions) {
		o.value = value
	}
}

// 应用选项并设置默认值
func applyLockOptions(opts []Option) *lockOptions {
	options := &lockOptions{
		acquireTimeout:   DefaultAcquireTimeout,
		lockTTL:          DefaultLockTTL,
		enableWatchdog:   true,
		watchdogInterval: DefaultWatchdogInterval,
		retryInterval:    DefaultRetryInterval,
		value:            generateUniqueValue(),
	}

	for _, opt := range opts {
		opt(options)
	}

	// 智能设置看门狗间隔
	if options.watchdogInterval == DefaultWatchdogInterval {
		options.watchdogInterval = options.lockTTL / 3
	}

	return options
}

func applyRWLockOptions(opts []RWOption) *rwLockOptions {
	options := &rwLockOptions{
		acquireTimeout:   DefaultAcquireTimeout,
		lockTTL:          DefaultLockTTL,
		enableWatchdog:   true,
		watchdogInterval: DefaultWatchdogInterval,
		retryInterval:    DefaultRetryInterval,
		value:            generateUniqueValue(),
	}

	for _, opt := range opts {
		opt(options)
	}

	if options.watchdogInterval == DefaultWatchdogInterval {
		options.watchdogInterval = options.lockTTL / 3
	}

	return options
}

func applyFairLockOptions(opts []FairOption) *fairLockOptions {
	options := &fairLockOptions{
		acquireTimeout:   DefaultAcquireTimeout,
		lockTTL:          DefaultLockTTL,
		enableWatchdog:   true,
		watchdogInterval: DefaultWatchdogInterval,
		retryInterval:    DefaultRetryInterval,
		queueTTL:         DefaultQueueTTL,
		value:            generateUniqueValue(),
	}
	for _, opt := range opts {
		opt(options)
	}
	if options.watchdogInterval == DefaultWatchdogInterval {
		options.watchdogInterval = options.lockTTL / 3
	}
	return options
}

// Lock 基本锁接口
type Lock interface {
	// Lock 加锁，使用函数式选项
	Lock(ctx context.Context, opts ...Option) error
	// TryLock 尝试加锁，立即返回结果
	TryLock(ctx context.Context, opts ...Option) (bool, error)
	// Unlock 解锁
	Unlock(ctx context.Context) error
	// Extend 续约锁，延长TTL
	Extend(ctx context.Context, ttl time.Duration) (bool, error)
	// IsLocked 检查锁是否被持有
	IsLocked(ctx context.Context) (bool, error)
	// GetTTL 获取锁的剩余时间
	GetTTL(ctx context.Context) (time.Duration, error)
	// GetKey 获取锁的键名
	GetKey() string
	// GetValue 获取锁的值（持有者标识）
	GetValue() string
}

// RWLock 读写锁接口
type RWLock interface {
	// RLock 获取读锁
	RLock(ctx context.Context, opts ...RWOption) error
	// TryRLock 尝试获取读锁
	TryRLock(ctx context.Context, opts ...RWOption) (bool, error)
	// RUnlock 释放读锁
	RUnlock(ctx context.Context) error

	// WLock 获取写锁
	WLock(ctx context.Context, opts ...RWOption) error
	// TryWLock 尝试获取写锁
	TryWLock(ctx context.Context, opts ...RWOption) (bool, error)
	// WUnlock 释放写锁
	WUnlock(ctx context.Context) error

	// GetReadCount 获取当前读锁数量
	GetReadCount(ctx context.Context) (int, error)
	// IsWriteLocked 检查是否有写锁
	IsWriteLocked(ctx context.Context) (bool, error)
	// GetKey 获取锁的键名
	GetKey() string
}

// FairLock 公平锁接口，确保先到先得
type FairLock interface {
	// Lock 获取公平锁
	Lock(ctx context.Context, opts ...FairOption) error
	// TryLock 尝试获取公平锁
	TryLock(ctx context.Context, opts ...FairOption) (bool, error)
	// Unlock 释放锁
	Unlock(ctx context.Context) error
	// Extend 续约锁
	Extend(ctx context.Context, ttl time.Duration) (bool, error)
	// IsLocked 检查锁是否被持有
	IsLocked(ctx context.Context) (bool, error)
	// GetTTL 获取锁的剩余时间
	GetTTL(ctx context.Context) (time.Duration, error)
	// GetQueuePosition 获取在等待队列中的位置，0表示当前持有锁
	GetQueuePosition(ctx context.Context) (int, error)
	// GetQueueLength 获取等待队列长度
	GetQueueLength(ctx context.Context) (int, error)
	// GetQueueInfo 获取队列详细信息
	GetQueueInfo(ctx context.Context) ([]string, error)
	// GetKey 获取锁的键名
	GetKey() string
	// GetValue 获取锁的值
	GetValue() string
}

// Watchdog 看门狗接口，用于自动续约
type Watchdog interface {
	// Start 启动看门狗
	Start(ctx context.Context) error
	// Stop 停止看门狗
	Stop() error
	// IsRunning 检查看门狗是否在运行
	IsRunning() bool
	// GetStats 获取看门狗统计信息
	GetStats() WatchdogStats
}

// WatchdogStats 看门狗统计信息
type WatchdogStats struct {
	StartTime        time.Time     // 启动时间
	ExtendCount      int64         // 续约次数
	FailedExtends    int64         // 失败的续约次数
	LastExtendTime   time.Time     // 最后续约时间
	LastExtendResult bool          // 最后续约结果
	Interval         time.Duration // 续约间隔
}

// LockWithWatchdog 带看门狗的锁接口
type LockWithWatchdog interface {
	Lock
	Watchdog
}

// LockType 锁类型
type LockType int

const (
	// TypeSimple 简单互斥锁
	TypeSimple LockType = iota
	// TypeFair 公平锁（FIFO）
	TypeFair
	// TypeRW 读写锁
	TypeRW
)

func (lt LockType) String() string {
	switch lt {
	case TypeSimple:
		return "Simple"
	case TypeFair:
		return "Fair"
	case TypeRW:
		return "ReadWrite"
	default:
		return "Unknown"
	}
}

// Backend 后端存储
type Backend interface {
	Type() string
}

// RedisBackend Redis后端
type RedisBackend struct {
	Client redis.UniversalClient
}

func (r *RedisBackend) Type() string {
	return "Redis"
}

// LocalBackend 本地内存后端
type LocalBackend struct {
	// ShardCount 分片数量，用于减少锁竞争
	ShardCount int
}

func (l *LocalBackend) Type() string {
	return "Local"
}

// NewRedisBackend 创建Redis后端
func NewRedisBackend(client redis.UniversalClient) *RedisBackend {
	return &RedisBackend{
		Client: client,
	}
}

// NewLocalBackend 创建本地后端
func NewLocalBackend(shardCount int) *LocalBackend {
	if shardCount <= 0 {
		shardCount = DefaultShardCount
	}
	return &LocalBackend{
		ShardCount: shardCount,
	}
}

// 默认配置常量
var (
	DefaultAcquireTimeout   = 10 * time.Second
	DefaultLockTTL          = 30 * time.Second
	DefaultWatchdogInterval = 10 * time.Second
	DefaultRetryInterval    = 100 * time.Millisecond
	DefaultQueueTTL         = 5 * time.Minute
	DefaultShardCount       = 32
)

// 生成唯一值，用于标识锁的持有者
func generateUniqueValue() string {
	hostname, _ := os.Hostname()

	// 生成随机字节
	b := make([]byte, 8)
	if _, err := rand.Read(b); err != nil {
		return fmt.Sprintf("%s-%d", hostname, time.Now().UnixNano())
	}

	return fmt.Sprintf("%s-%x", hostname, b)
}

func NewSimpleLock(key string, backend Backend) (Lock, error) {
	switch backend.Type() {
	case "Redis":
		redisBackend := backend.(*RedisBackend)
		return newRedisSimpleLock(key, redisBackend.Client), nil
	case "Local":
		localBackend := backend.(*LocalBackend)
		return newLocalSimpleLock(key, localBackend), nil
	default:
		return nil, ErrUnsupportedBackend
	}
}

func NewRWLock(key string, backend Backend) (RWLock, error) {
	switch backend.Type() {
	case "Redis":
		redisBackend := backend.(*RedisBackend)
		return newRedisRWLock(key, redisBackend.Client), nil
	case "Local":
		localBackend := backend.(*LocalBackend)
		return newLocalRWLock(key, localBackend), nil
	default:
		return nil, ErrUnsupportedBackend
	}
}

// NewFairLock 创建公平锁实例
func NewFairLock(key string, backend Backend) (FairLock, error) {
	switch backend.Type() {
	case "Redis":
		redisBackend := backend.(*RedisBackend)
		return newRedisFairLock(key, redisBackend.Client), nil
	case "Local":
		localBackend := backend.(*LocalBackend)
		return newLocalFairLock(key, localBackend), nil
	default:
		return nil, ErrUnsupportedBackend
	}
}
