package lock

import (
	"context"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
)

const (
	// 读写锁的键名格式
	readLockKeyFormat  = "%s:read"  // 读锁键名格式
	writeLockKeyFormat = "%s:write" // 写锁键名格式
)

type redisRWLock struct {
	key    string
	client redis.UniversalClient
}

func newRedisRWLock(key string, client redis.UniversalClient) RWLock {
	return &redisRWLock{
		key:    key,
		client: client,
	}
}

// RLock 获取读锁
func (l *redisRWLock) RLock(ctx context.Context, opts ...RWOption) error {
	options := applyRWLockOptions(opts)
	deadline := time.Now().Add(options.acquireTimeout)

	for {
		// 检查是否超时
		if time.Now().After(deadline) {
			return ErrAcquireTimeout
		}

		// 尝试获取读锁
		acquired, err := l.TryRLock(ctx, opts...)
		if err != nil {
			return err
		}
		if acquired {
			return nil
		}

		// 等待一段时间后重试
		time.Sleep(options.retryInterval)
	}
}

// TryRLock 尝试获取读锁
func (l *redisRWLock) TryRLock(ctx context.Context, opts ...RWOption) (bool, error) {
	options := applyRWLockOptions(opts)
	readKey := fmt.Sprintf(readLockKeyFormat, l.key)
	writeKey := fmt.Sprintf(writeLockKeyFormat, l.key)

	// 使用 Lua 脚本确保原子性
	script := `
		if redis.call("EXISTS", KEYS[2]) == 0 then
			redis.call("INCR", KEYS[1])
			redis.call("EXPIRE", KEYS[1], ARGV[1])
			return 1
		end
		return 0
	`

	result, err := l.client.Eval(ctx, script, []string{readKey, writeKey}, options.lockTTL.Seconds()).Int()
	if err != nil {
		return false, err
	}

	return result == 1, nil
}

// RUnlock 释放读锁
func (l *redisRWLock) RUnlock(ctx context.Context) error {
	readKey := fmt.Sprintf(readLockKeyFormat, l.key)

	// 使用 Lua 脚本确保原子性
	script := `
		local count = redis.call("GET", KEYS[1])
		if not count then
			return 0
		end
		count = tonumber(count)
		if count > 1 then
			redis.call("DECR", KEYS[1])
			return 1
		else
			redis.call("DEL", KEYS[1])
			return 1
		end
	`

	result, err := l.client.Eval(ctx, script, []string{readKey}).Int()
	if err != nil {
		return err
	}

	if result == 0 {
		return ErrLockNotFound
	}

	return nil
}

// WLock 获取写锁
func (l *redisRWLock) WLock(ctx context.Context, opts ...RWOption) error {
	options := applyRWLockOptions(opts)
	deadline := time.Now().Add(options.acquireTimeout)

	for {
		// 检查是否超时
		if time.Now().After(deadline) {
			return ErrAcquireTimeout
		}

		// 尝试获取写锁
		acquired, err := l.TryWLock(ctx, opts...)
		if err != nil {
			return err
		}
		if acquired {
			return nil
		}

		// 等待一段时间后重试
		time.Sleep(options.retryInterval)
	}
}

// TryWLock 尝试获取写锁
func (l *redisRWLock) TryWLock(ctx context.Context, opts ...RWOption) (bool, error) {
	options := applyRWLockOptions(opts)
	readKey := fmt.Sprintf(readLockKeyFormat, l.key)
	writeKey := fmt.Sprintf(writeLockKeyFormat, l.key)

	// 使用 Lua 脚本确保原子性
	script := `
		if redis.call("EXISTS", KEYS[1]) == 0 and redis.call("EXISTS", KEYS[2]) == 0 then
			redis.call("SET", KEYS[2], ARGV[1], "EX", ARGV[2])
			return 1
		end
		return 0
	`

	result, err := l.client.Eval(ctx, script, []string{readKey, writeKey}, options.value, options.lockTTL.Seconds()).Int()
	if err != nil {
		return false, err
	}

	return result == 1, nil
}

// WUnlock 释放写锁
func (l *redisRWLock) WUnlock(ctx context.Context) error {
	writeKey := fmt.Sprintf(writeLockKeyFormat, l.key)
	options := applyRWLockOptions(nil) // 使用默认选项

	// 使用 Lua 脚本确保原子性
	script := `
		if redis.call("GET", KEYS[1]) == ARGV[1] then
			redis.call("DEL", KEYS[1])
			return 1
		end
		return 0
	`

	result, err := l.client.Eval(ctx, script, []string{writeKey}, options.value).Int()
	if err != nil {
		return err
	}

	if result == 0 {
		return ErrLockNotFound
	}

	return nil
}

// GetReadCount 获取当前读锁数量
func (l *redisRWLock) GetReadCount(ctx context.Context) (int, error) {
	readKey := fmt.Sprintf(readLockKeyFormat, l.key)
	count, err := l.client.Get(ctx, readKey).Int()
	if err == redis.Nil {
		return 0, nil
	}
	if err != nil {
		return 0, err
	}
	return count, nil
}

// IsWriteLocked 检查是否有写锁
func (l *redisRWLock) IsWriteLocked(ctx context.Context) (bool, error) {
	writeKey := fmt.Sprintf(writeLockKeyFormat, l.key)
	exists, err := l.client.Exists(ctx, writeKey).Result()
	if err != nil {
		return false, err
	}
	return exists == 1, nil
}

// GetKey 获取锁的键名
func (l *redisRWLock) GetKey() string {
	return l.key
}
