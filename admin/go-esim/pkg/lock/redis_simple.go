package lock

import (
	"context"
	"time"

	"github.com/redis/go-redis/v9"
)

type redisSimpleLock struct {
	key    string
	value  string
	client redis.UniversalClient
}

func newRedisSimpleLock(key string, client redis.UniversalClient) Lock {
	return &redisSimpleLock{
		key:    key,
		client: client,
	}
}

// Lock 获取锁，如果获取失败会重试直到超时
func (l *redisSimpleLock) Lock(ctx context.Context, opts ...Option) error {
	options := applyLockOptions(opts)
	deadline := time.Now().Add(options.acquireTimeout)

	for {
		// 检查是否超时
		if time.Now().After(deadline) {
			return ErrAcquireTimeout
		}

		// 尝试获取锁
		acquired, err := l.TryLock(ctx, opts...)
		if err != nil {
			return err
		}
		if acquired {
			return nil
		}

		// 等待一段时间后重试
		time.Sleep(options.retryInterval)
	}
}

// TryLock 尝试获取锁，立即返回结果
func (l *redisSimpleLock) TryLock(ctx context.Context, opts ...Option) (bool, error) {
	options := applyLockOptions(opts)
	l.value = options.value // 保存 value 用于后续解锁

	// 使用 Lua 脚本确保原子性
	script := `
		if redis.call("SET", KEYS[1], ARGV[1], "NX", "EX", ARGV[2]) then
			return 1
		end
		return 0
	`

	result, err := l.client.Eval(ctx, script, []string{l.key}, options.value, options.lockTTL.Seconds()).Int()
	if err != nil {
		return false, err
	}

	return result == 1, nil
}

// Unlock 释放锁
func (l *redisSimpleLock) Unlock(ctx context.Context) error {
	// 使用 Lua 脚本确保原子性
	script := `
		if redis.call("GET", KEYS[1]) == ARGV[1] then
			redis.call("DEL", KEYS[1])
			return 1
		end
		return 0
	`

	result, err := l.client.Eval(ctx, script, []string{l.key}, l.value).Int()
	if err != nil {
		return err
	}

	if result == 0 {
		return ErrLockNotFound
	}

	return nil
}

// Extend 续约锁，延长 TTL
func (l *redisSimpleLock) Extend(ctx context.Context, ttl time.Duration) (bool, error) {
	// 使用 Lua 脚本确保原子性
	script := `
		if redis.call("GET", KEYS[1]) == ARGV[1] then
			redis.call("EXPIRE", KEYS[1], ARGV[2])
			return 1
		end
		return 0
	`

	result, err := l.client.Eval(ctx, script, []string{l.key}, l.value, ttl.Seconds()).Int()
	if err != nil {
		return false, err
	}

	return result == 1, nil
}

// IsLocked 检查锁是否被持有
func (l *redisSimpleLock) IsLocked(ctx context.Context) (bool, error) {
	exists, err := l.client.Exists(ctx, l.key).Result()
	if err != nil {
		return false, err
	}
	return exists == 1, nil
}

// GetTTL 获取锁的剩余时间
func (l *redisSimpleLock) GetTTL(ctx context.Context) (time.Duration, error) {
	ttl, err := l.client.TTL(ctx, l.key).Result()
	if err != nil {
		return 0, err
	}
	return ttl, nil
}

// GetKey 获取锁的键名
func (l *redisSimpleLock) GetKey() string {
	return l.key
}

// GetValue 获取锁的值（持有者标识）
func (l *redisSimpleLock) GetValue() string {
	return l.value
}
