package lock

import (
	"context"
	"sync"
	"time"
)

type localFairLock struct {
	key      string
	value    string
	mu       sync.Mutex
	unlocked bool
	ttl      time.Duration
	expireAt time.Time
	queue    []string // 等待队列
}

func newLocalFairLock(key string, backend *LocalBackend) FairLock {
	return &localFairLock{
		key:      key,
		unlocked: true,
		queue:    make([]string, 0),
	}
}

// Lock 获取公平锁
func (l *localFairLock) Lock(ctx context.Context, opts ...FairOption) error {
	options := applyFairLockOptions(opts)
	deadline := time.Now().Add(options.acquireTimeout)

	for {
		// 检查是否超时
		if time.Now().After(deadline) {
			return ErrAcquireTimeout
		}

		// 尝试获取锁
		acquired, err := l.TryLock(ctx, opts...)
		if err != nil {
			return err
		}
		if acquired {
			return nil
		}

		// 等待一段时间后重试
		time.Sleep(options.retryInterval)
	}
}

// TryLock 尝试获取公平锁
func (l *localFairLock) TryLock(ctx context.Context, opts ...FairOption) (bool, error) {
	options := applyFairLockOptions(opts)

	l.mu.Lock()
	defer l.mu.Unlock()

	// 如果锁已经被持有，检查是否过期
	if !l.unlocked {
		if time.Now().After(l.expireAt) {
			// 锁已过期，可以重新获取
			l.unlocked = true
			l.queue = l.queue[1:] // 移除队首
		} else {
			// 将当前请求加入队列
			l.queue = append(l.queue, options.value)
			return false, nil
		}
	}

	// 检查是否是队列中的第一个
	if len(l.queue) > 0 && l.queue[0] != options.value {
		// 不是第一个，加入队列
		l.queue = append(l.queue, options.value)
		return false, nil
	}

	// 获取锁
	l.unlocked = false
	l.value = options.value
	l.ttl = options.lockTTL
	l.expireAt = time.Now().Add(options.lockTTL)

	return true, nil
}

// Unlock 释放锁
func (l *localFairLock) Unlock(ctx context.Context) error {
	l.mu.Lock()
	defer l.mu.Unlock()

	if l.unlocked {
		return ErrLockNotFound
	}

	l.unlocked = true
	if len(l.queue) > 0 {
		l.queue = l.queue[1:] // 移除队首
	}
	return nil
}

// Extend 续约锁
func (l *localFairLock) Extend(ctx context.Context, ttl time.Duration) (bool, error) {
	l.mu.Lock()
	defer l.mu.Unlock()

	if l.unlocked {
		return false, ErrLockNotFound
	}

	l.ttl = ttl
	l.expireAt = time.Now().Add(ttl)
	return true, nil
}

// IsLocked 检查锁是否被持有
func (l *localFairLock) IsLocked(ctx context.Context) (bool, error) {
	l.mu.Lock()
	defer l.mu.Unlock()

	if l.unlocked {
		return false, nil
	}

	// 检查是否过期
	if time.Now().After(l.expireAt) {
		l.unlocked = true
		if len(l.queue) > 0 {
			l.queue = l.queue[1:] // 移除队首
		}
		return false, nil
	}

	return true, nil
}

// GetTTL 获取锁的剩余时间
func (l *localFairLock) GetTTL(ctx context.Context) (time.Duration, error) {
	l.mu.Lock()
	defer l.mu.Unlock()

	if l.unlocked {
		return 0, ErrLockNotFound
	}

	// 检查是否过期
	if time.Now().After(l.expireAt) {
		l.unlocked = true
		if len(l.queue) > 0 {
			l.queue = l.queue[1:] // 移除队首
		}
		return 0, ErrLockExpired
	}

	return time.Until(l.expireAt), nil
}

// GetQueuePosition 获取在等待队列中的位置，0表示当前持有锁
func (l *localFairLock) GetQueuePosition(ctx context.Context) (int, error) {
	l.mu.Lock()
	defer l.mu.Unlock()

	if !l.unlocked && l.value == l.queue[0] {
		return 0, nil
	}

	for i, v := range l.queue {
		if v == l.value {
			return i + 1, nil
		}
	}

	return -1, ErrLockNotFound
}

// GetQueueLength 获取等待队列长度
func (l *localFairLock) GetQueueLength(ctx context.Context) (int, error) {
	l.mu.Lock()
	defer l.mu.Unlock()

	return len(l.queue), nil
}

// GetQueueInfo 获取队列详细信息
func (l *localFairLock) GetQueueInfo(ctx context.Context) ([]string, error) {
	l.mu.Lock()
	defer l.mu.Unlock()

	// 创建队列的副本
	queue := make([]string, len(l.queue))
	copy(queue, l.queue)
	return queue, nil
}

// GetKey 获取锁的键名
func (l *localFairLock) GetKey() string {
	return l.key
}

// GetValue 获取锁的值
func (l *localFairLock) GetValue() string {
	return l.value
}
