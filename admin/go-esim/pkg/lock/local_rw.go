package lock

import (
	"context"
	"sync"
	"sync/atomic"
	"time"
)

type localRWLock struct {
	key      string
	value    string
	mu       sync.RWMutex
	unlocked bool
	ttl      time.Duration
	expireAt time.Time
	readers  int32 // 使用原子操作计数读锁数量
}

func newLocalRWLock(key string, backend *LocalBackend) RWLock {
	return &localRWLock{
		key:      key,
		unlocked: true,
	}
}

// Lock 获取写锁
func (l *localRWLock) Lock(ctx context.Context, opts ...Option) error {
	options := applyLockOptions(opts)
	deadline := time.Now().Add(options.acquireTimeout)

	for {
		// 检查是否超时
		if time.Now().After(deadline) {
			return ErrAcquireTimeout
		}

		// 尝试获取锁
		acquired, err := l.TryLock(ctx, opts...)
		if err != nil {
			return err
		}
		if acquired {
			return nil
		}

		// 等待一段时间后重试
		time.Sleep(options.retryInterval)
	}
}

// TryLock 尝试获取写锁
func (l *localRWLock) TryLock(ctx context.Context, opts ...Option) (bool, error) {
	options := applyLockOptions(opts)

	l.mu.Lock()
	defer l.mu.Unlock()

	// 如果锁已经被持有，检查是否过期
	if !l.unlocked {
		if time.Now().After(l.expireAt) {
			// 锁已过期，可以重新获取
			l.unlocked = true
		} else {
			return false, nil
		}
	}

	// 获取锁
	l.unlocked = false
	l.value = options.value
	l.ttl = options.lockTTL
	l.expireAt = time.Now().Add(options.lockTTL)

	return true, nil
}

// Unlock 释放写锁
func (l *localRWLock) Unlock(ctx context.Context) error {
	l.mu.Lock()
	defer l.mu.Unlock()

	if l.unlocked {
		return ErrLockNotFound
	}

	l.unlocked = true
	return nil
}

// Extend 续约写锁
func (l *localRWLock) Extend(ctx context.Context, ttl time.Duration) (bool, error) {
	l.mu.Lock()
	defer l.mu.Unlock()

	if l.unlocked {
		return false, ErrLockNotFound
	}

	l.ttl = ttl
	l.expireAt = time.Now().Add(ttl)
	return true, nil
}

// IsLocked 检查写锁是否被持有
func (l *localRWLock) IsLocked(ctx context.Context) (bool, error) {
	l.mu.RLock()
	defer l.mu.RUnlock()

	if l.unlocked {
		return false, nil
	}

	// 检查是否过期
	if time.Now().After(l.expireAt) {
		l.unlocked = true
		return false, nil
	}

	return true, nil
}

// GetTTL 获取写锁的剩余时间
func (l *localRWLock) GetTTL(ctx context.Context) (time.Duration, error) {
	l.mu.RLock()
	defer l.mu.RUnlock()

	if l.unlocked {
		return 0, ErrLockNotFound
	}

	// 检查是否过期
	if time.Now().After(l.expireAt) {
		l.unlocked = true
		return 0, ErrLockExpired
	}

	return time.Until(l.expireAt), nil
}

// RLock 获取读锁
func (l *localRWLock) RLock(ctx context.Context, opts ...RWOption) error {
	options := applyRWLockOptions(opts)
	deadline := time.Now().Add(options.acquireTimeout)

	for {
		// 检查是否超时
		if time.Now().After(deadline) {
			return ErrAcquireTimeout
		}

		// 尝试获取读锁
		acquired, err := l.TryRLock(ctx, opts...)
		if err != nil {
			return err
		}
		if acquired {
			return nil
		}

		// 等待一段时间后重试
		time.Sleep(options.retryInterval)
	}
}

// TryRLock 尝试获取读锁
func (l *localRWLock) TryRLock(ctx context.Context, opts ...RWOption) (bool, error) {
	l.mu.RLock()
	defer l.mu.RUnlock()

	// 如果写锁被持有，检查是否过期
	if !l.unlocked {
		if time.Now().After(l.expireAt) {
			// 写锁已过期，可以获取读锁
			l.unlocked = true
		} else {
			return false, nil
		}
	}

	atomic.AddInt32(&l.readers, 1)
	return true, nil
}

// RUnlock 释放读锁
func (l *localRWLock) RUnlock(ctx context.Context) error {
	atomic.AddInt32(&l.readers, -1)
	l.mu.RUnlock()
	return nil
}

// GetReadCount 获取当前读锁数量
func (l *localRWLock) GetReadCount(ctx context.Context) (int, error) {
	return int(atomic.LoadInt32(&l.readers)), nil
}

// GetKey 获取锁的键名
func (l *localRWLock) GetKey() string {
	return l.key
}

// GetValue 获取锁的值
func (l *localRWLock) GetValue() string {
	return l.value
}

// IsWriteLocked 检查写锁是否被持有
func (l *localRWLock) IsWriteLocked(ctx context.Context) (bool, error) {
	return l.IsLocked(ctx)
}

// WLock 获取写锁
func (l *localRWLock) WLock(ctx context.Context, opts ...RWOption) error {
	options := applyRWLockOptions(opts)
	return l.Lock(ctx, WithLockTTL(options.lockTTL), WithAcquireTimeout(options.acquireTimeout))
}

// WUnlock 释放写锁
func (l *localRWLock) WUnlock(ctx context.Context) error {
	return l.Unlock(ctx)
}

// TryWLock 尝试获取写锁
func (l *localRWLock) TryWLock(ctx context.Context, opts ...RWOption) (bool, error) {
	options := applyRWLockOptions(opts)
	return l.TryLock(ctx, WithLockTTL(options.lockTTL), WithAcquireTimeout(options.acquireTimeout))
}
