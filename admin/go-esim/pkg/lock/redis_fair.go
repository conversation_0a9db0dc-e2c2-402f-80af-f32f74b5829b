package lock

import (
	"context"
	"errors"
	"time"

	"github.com/redis/go-redis/v9"
)

type redisFairLock struct {
	key      string
	queueKey string
	value    string
	client   redis.UniversalClient
}

func newRedisFairLock(key string, client redis.UniversalClient) FairLock {
	return &redisFairLock{
		key:      key,
		queueKey: key + ":queue",
		client:   client,
	}
}

// Lock 公平加锁
func (l *redisFairLock) Lock(ctx context.Context, opts ...FairOption) error {
	options := applyFairLockOptions(opts)
	l.value = options.value
	queueTTL := options.queueTTL
	if queueTTL <= 0 {
		queueTTL = 5 * time.Minute
	}

	// 1. 入队
	if err := l.client.RPush(ctx, l.queueKey, l.value).Err(); err != nil {
		return err
	}
	// 设置队列元素过期，防止死队列
	_ = l.client.Expire(ctx, l.queueKey, queueTTL).Err()

	acquireTimeout := options.acquireTimeout
	if acquireTimeout <= 0 {
		acquireTimeout = 10 * time.Second
	}
	lockTTL := options.lockTTL
	if lockTTL <= 0 {
		lockTTL = 30 * time.Second
	}

	deadline := time.Now().Add(acquireTimeout)
	for {
		// 2. 检查自己是否在队首
		first, err := l.client.LIndex(ctx, l.queueKey, 0).Result()
		if err == redis.Nil {
			// 队列被清空，重试
			continue
		} else if err != nil {
			return err
		}
		if first == l.value {
			// 3. 只有队首才有资格尝试加锁
			ok, err := l.client.SetNX(ctx, l.key, l.value, lockTTL).Result()
			if err != nil {
				return err
			}
			if ok {
				// 加锁成功，出队
				_, _ = l.client.LPop(ctx, l.queueKey).Result()
				return nil
			}
		}
		// 4. 未到自己或加锁失败，等待重试
		if time.Now().After(deadline) {
			// 超时，出队
			_ = l.client.LRem(ctx, l.queueKey, 0, l.value).Err()
			return ErrAcquireTimeout
		}
		time.Sleep(50 * time.Millisecond)
	}
}

// TryLock 公平尝试加锁（不等待）
func (l *redisFairLock) TryLock(ctx context.Context, opts ...FairOption) (bool, error) {
	options := applyFairLockOptions(opts)
	l.value = options.value
	queueTTL := options.queueTTL
	if queueTTL <= 0 {
		queueTTL = 5 * time.Minute
	}
	// 入队
	if err := l.client.RPush(ctx, l.queueKey, l.value).Err(); err != nil {
		return false, err
	}
	_ = l.client.Expire(ctx, l.queueKey, queueTTL).Err()
	// 检查队首
	first, err := l.client.LIndex(ctx, l.queueKey, 0).Result()
	if err != nil {
		_ = l.client.LRem(ctx, l.queueKey, 0, l.value).Err()
		return false, err
	}
	if first == l.value {
		lockTTL := options.lockTTL
		if lockTTL <= 0 {
			lockTTL = 30 * time.Second
		}
		ok, err := l.client.SetNX(ctx, l.key, l.value, lockTTL).Result()
		if err != nil {
			_ = l.client.LRem(ctx, l.queueKey, 0, l.value).Err()
			return false, err
		}
		if ok {
			_, _ = l.client.LPop(ctx, l.queueKey).Result()
			return true, nil
		}
	}
	// 不是队首或加锁失败，出队
	_ = l.client.LRem(ctx, l.queueKey, 0, l.value).Err()
	return false, nil
}

// Unlock 公平解锁
func (l *redisFairLock) Unlock(ctx context.Context) error {
	// 只允许持有者解锁
	script := redis.NewScript(`
		if redis.call('get', KEYS[1]) == ARGV[1] then
			redis.call('del', KEYS[1])
			return 1
		else
			return 0
		end
	`)
	res, err := script.Run(ctx, l.client, []string{l.key}, l.value).Int()
	if err != nil {
		return err
	}
	// 无论如何都尝试从队列移除自己
	_ = l.client.LRem(ctx, l.queueKey, 0, l.value).Err()
	if res == 1 {
		return nil
	}
	return errors.New("not lock holder or already released")
}

// Extend 续约
func (l *redisFairLock) Extend(ctx context.Context, ttl time.Duration) (bool, error) {
	script := redis.NewScript(`
		if redis.call('get', KEYS[1]) == ARGV[1] then
			return redis.call('pexpire', KEYS[1], ARGV[2])
		else
			return 0
		end
	`)
	ms := int(ttl.Milliseconds())
	res, err := script.Run(ctx, l.client, []string{l.key}, l.value, ms).Int()
	if err != nil {
		return false, err
	}
	return res == 1, nil
}

// IsLocked 判断锁是否被持有
func (l *redisFairLock) IsLocked(ctx context.Context) (bool, error) {
	val, err := l.client.Get(ctx, l.key).Result()
	if err == redis.Nil {
		return false, nil
	}
	if err != nil {
		return false, err
	}
	return val != "", nil
}

// GetTTL 获取锁的剩余时间
func (l *redisFairLock) GetTTL(ctx context.Context) (time.Duration, error) {
	ttl, err := l.client.PTTL(ctx, l.key).Result()
	if err != nil {
		return 0, err
	}
	return ttl, nil
}

// GetQueuePosition 获取自己在队列中的位置（0为队首）
func (l *redisFairLock) GetQueuePosition(ctx context.Context) (int, error) {
	list, err := l.client.LRange(ctx, l.queueKey, 0, -1).Result()
	if err != nil {
		return -1, err
	}
	for i, v := range list {
		if v == l.value {
			return i, nil
		}
	}
	return -1, nil // 不在队列中
}

// GetQueueLength 获取队列长度
func (l *redisFairLock) GetQueueLength(ctx context.Context) (int, error) {
	len, err := l.client.LLen(ctx, l.queueKey).Result()
	if err != nil {
		return 0, err
	}
	return int(len), nil
}

// GetQueueInfo 获取队列所有元素
func (l *redisFairLock) GetQueueInfo(ctx context.Context) ([]string, error) {
	return l.client.LRange(ctx, l.queueKey, 0, -1).Result()
}

func (l *redisFairLock) GetKey() string   { return l.key }
func (l *redisFairLock) GetValue() string { return l.value }
