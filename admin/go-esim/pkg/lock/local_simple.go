package lock

import (
	"context"
	"sync"
	"time"
)

type localSimpleLock struct {
	key      string
	value    string
	mu       sync.Mutex
	unlocked bool
	ttl      time.Duration
	expireAt time.Time
}

func newLocalSimpleLock(key string, backend *LocalBackend) Lock {
	return &localSimpleLock{
		key:      key,
		unlocked: true,
	}
}

// Lock 获取锁，如果获取失败会重试直到超时
func (l *localSimpleLock) Lock(ctx context.Context, opts ...Option) error {
	options := applyLockOptions(opts)
	deadline := time.Now().Add(options.acquireTimeout)

	for {
		// 检查是否超时
		if time.Now().After(deadline) {
			return ErrAcquireTimeout
		}

		// 尝试获取锁
		acquired, err := l.TryLock(ctx, opts...)
		if err != nil {
			return err
		}
		if acquired {
			return nil
		}

		// 等待一段时间后重试
		time.Sleep(options.retryInterval)
	}
}

// TryLock 尝试获取锁，立即返回结果
func (l *localSimpleLock) TryLock(ctx context.Context, opts ...Option) (bool, error) {
	options := applyLockOptions(opts)

	l.mu.Lock()
	defer l.mu.Unlock()

	// 如果锁已经被持有，检查是否过期
	if !l.unlocked {
		if time.Now().After(l.expireAt) {
			// 锁已过期，可以重新获取
			l.unlocked = true
		} else {
			return false, nil
		}
	}

	// 获取锁
	l.unlocked = false
	l.value = options.value
	l.ttl = options.lockTTL
	l.expireAt = time.Now().Add(options.lockTTL)

	return true, nil
}

// Unlock 释放锁
func (l *localSimpleLock) Unlock(ctx context.Context) error {
	l.mu.Lock()
	defer l.mu.Unlock()

	if l.unlocked {
		return ErrLockNotFound
	}

	l.unlocked = true
	return nil
}

// Extend 续约锁，延长 TTL
func (l *localSimpleLock) Extend(ctx context.Context, ttl time.Duration) (bool, error) {
	l.mu.Lock()
	defer l.mu.Unlock()

	if l.unlocked {
		return false, ErrLockNotFound
	}

	l.ttl = ttl
	l.expireAt = time.Now().Add(ttl)
	return true, nil
}

// IsLocked 检查锁是否被持有
func (l *localSimpleLock) IsLocked(ctx context.Context) (bool, error) {
	l.mu.Lock()
	defer l.mu.Unlock()

	if l.unlocked {
		return false, nil
	}

	// 检查是否过期
	if time.Now().After(l.expireAt) {
		l.unlocked = true
		return false, nil
	}

	return true, nil
}

// GetTTL 获取锁的剩余时间
func (l *localSimpleLock) GetTTL(ctx context.Context) (time.Duration, error) {
	l.mu.Lock()
	defer l.mu.Unlock()

	if l.unlocked {
		return 0, ErrLockNotFound
	}

	// 检查是否过期
	if time.Now().After(l.expireAt) {
		l.unlocked = true
		return 0, ErrLockExpired
	}

	return time.Until(l.expireAt), nil
}

// GetKey 获取锁的键名
func (l *localSimpleLock) GetKey() string {
	return l.key
}

// GetValue 获取锁的值（持有者标识）
func (l *localSimpleLock) GetValue() string {
	return l.value
}
