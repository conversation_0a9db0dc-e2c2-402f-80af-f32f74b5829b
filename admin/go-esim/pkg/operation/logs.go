package operation

import (
	"context"
	"errors"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

// 发送操作日志
func DBLog(ctx context.Context, db *pgxpool.Pool, operationType string, targetId *int32, targetTable string, desc string, extData map[string]interface{}) error {
	// 从ctx中获取operator_id
	operatorId, ok := ctx.Value("userId").(string)
	if !ok {
		return errors.New("操作人ID不存在")
	}
	// 从ctx中获取operator_role

	operatorRole := "管理员"

	// 如果targetId为nil，设置为0
	var targetIdValue int32
	if targetId != nil {
		targetIdValue = *targetId
	} else {
		targetIdValue = 0
	}

	// 准备插入参数
	args := make(pgx.NamedArgs)
	args["operation_type"] = operationType
	args["target_id"] = targetIdValue
	args["target_table"] = targetTable
	args["description"] = desc
	args["operator_id"] = operatorId
	args["operator_role"] = operatorRole
	args["extra_data"] = extData
	ip, ok := ctx.Value("ip").(string)
	if !ok {
		ip = "0.0.0.0"
	}
	args["ip"] = ip
	// 执行插入操作
	query := `
		INSERT INTO iot.operation_logs (
			operation_type, 
			target_id, 
			target_table, 
			description, 
			operator_id, 
			operator_role, 
			extra_data,
			ip
		) VALUES (
			@operation_type, 
			@target_id, 
			@target_table, 
			@description, 
			@operator_id, 
			@operator_role, 
			@extra_data,
			@ip
		)
	`

	_, err := db.Exec(ctx, query, args)
	return err
}
