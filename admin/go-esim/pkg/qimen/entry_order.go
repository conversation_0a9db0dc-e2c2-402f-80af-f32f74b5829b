package qimen

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/Wenpiner/iot-api/pkg/constants"
)

// EntryOrderCreateRequest 创建入库单请求
type EntryOrderCreateRequest struct {
	EntryOrderCode   string                    `json:"entryOrderCode"`   // 入库单号
	WarehouseCode    string                    `json:"warehouseCode"`    // 仓库编码
	OwnerCode        string                    `json:"ownerCode"`        // 货主编码
	OrderType        string                    `json:"orderType"`        // 业务类型
	ExpectStartTime  string                    `json:"expectStartTime"`  // 预期开始时间
	ExpectEndTime    string                    `json:"expectEndTime"`    // 预期结束时间
	LogisticsCode    string                    `json:"logisticsCode"`    // 物流公司编码
	LogisticsName    string                    `json:"logisticsName"`    // 物流公司名称
	ExpressCode      string                    `json:"expressCode"`      // 运单号
	SupplierCode     string                    `json:"supplierCode"`     // 供应商编码
	SupplierName     string                    `json:"supplierName"`     // 供应商名称
	PurchaseOrderCode string                   `json:"purchaseOrderCode"` // 采购单号
	Remark           string                    `json:"remark"`           // 备注
	OrderLines       []EntryOrderLineRequest  `json:"orderLines"`       // 入库单明细
}

// EntryOrderLineRequest 入库单明细请求
type EntryOrderLineRequest struct {
	OrderLineNo    string  `json:"orderLineNo"`    // 单据行号
	OutBizCode     string  `json:"outBizCode"`     // 外部业务编码
	ItemCode       string  `json:"itemCode"`       // 商品编码
	ItemName       string  `json:"itemName"`       // 商品名称
	PlanQty        int32   `json:"planQty"`        // 计划数量
	PurchasePrice  float64 `json:"purchasePrice"`  // 采购价格
	RetailPrice    float64 `json:"retailPrice"`    // 零售价格
	InventoryType  string  `json:"inventoryType"`  // 库存类型
	BatchCode      string  `json:"batchCode"`      // 批次编码
	ProduceCode    string  `json:"produceCode"`    // 生产批号
	ProductDate    string  `json:"productDate"`    // 生产日期
	ExpireDate     string  `json:"expireDate"`     // 过期日期
	Unit           string  `json:"unit"`           // 单位
}

// EntryOrderCreateResponse 创建入库单响应
type EntryOrderCreateResponse struct {
	BaseResponse
	EntryOrderID string `json:"entryOrderId"` // 奇门系统入库单ID
}

// EntryOrderConfirmRequest 确认入库请求
type EntryOrderConfirmRequest struct {
	EntryOrderCode    string                      `json:"entryOrderCode"`    // 入库单号
	WarehouseCode     string                      `json:"warehouseCode"`     // 仓库编码
	OwnerCode         string                      `json:"ownerCode"`         // 货主编码
	ActualArrivalTime string                      `json:"actualArrivalTime"` // 实际到货时间
	OperatorName      string                      `json:"operatorName"`      // 操作员姓名
	Remark            string                      `json:"remark"`            // 备注
	OrderLines        []EntryOrderConfirmLineRequest `json:"orderLines"`        // 确认明细
}

// EntryOrderConfirmLineRequest 入库确认明细请求
type EntryOrderConfirmLineRequest struct {
	OrderLineNo     string `json:"orderLineNo"`     // 单据行号
	ItemCode        string `json:"itemCode"`        // 商品编码
	ActualQty       int32  `json:"actualQty"`       // 实际入库数量
	ShelfLocation   string `json:"shelfLocation"`   // 上架位置
	InboundTime     string `json:"inboundTime"`     // 入库时间
	Remark          string `json:"remark"`          // 备注
}

// EntryOrderConfirmResponse 确认入库响应
type EntryOrderConfirmResponse struct {
	BaseResponse
}

// CreateEntryOrder 创建入库单
func (c *Client) CreateEntryOrder(ctx context.Context, config *WarehouseConfig, req *EntryOrderCreateRequest) (*EntryOrderCreateResponse, error) {
	// 验证配置
	if err := c.ValidateConfig(config); err != nil {
		return nil, err
	}

	// 验证请求参数
	if err := c.validateEntryOrderCreateRequest(req); err != nil {
		return nil, err
	}

	// 调用API
	responseBody, err := c.CallAPIWithRetry(ctx, constants.QimenApiEntryOrderCreate, config, req, 3)
	if err != nil {
		return nil, err
	}

	// 解析响应
	var resp EntryOrderCreateResponse
	if err := c.ParseResponse(responseBody, &resp); err != nil {
		return nil, err
	}

	return &resp, nil
}

// ConfirmEntryOrder 确认入库
func (c *Client) ConfirmEntryOrder(ctx context.Context, config *WarehouseConfig, req *EntryOrderConfirmRequest) (*EntryOrderConfirmResponse, error) {
	// 验证配置
	if err := c.ValidateConfig(config); err != nil {
		return nil, err
	}

	// 验证请求参数
	if err := c.validateEntryOrderConfirmRequest(req); err != nil {
		return nil, err
	}

	// 调用API
	responseBody, err := c.CallAPIWithRetry(ctx, constants.QimenApiEntryOrderConfirm, config, req, 3)
	if err != nil {
		return nil, err
	}

	// 解析响应
	var resp EntryOrderConfirmResponse
	if err := c.ParseResponse(responseBody, &resp); err != nil {
		return nil, err
	}

	return &resp, nil
}

// validateEntryOrderCreateRequest 验证创建入库单请求
func (c *Client) validateEntryOrderCreateRequest(req *EntryOrderCreateRequest) error {
	if req == nil {
		return fmt.Errorf("请求参数不能为空")
	}
	if req.EntryOrderCode == "" {
		return fmt.Errorf("入库单号不能为空")
	}
	if req.WarehouseCode == "" {
		return fmt.Errorf("仓库编码不能为空")
	}
	if req.OwnerCode == "" {
		return fmt.Errorf("货主编码不能为空")
	}
	if req.OrderType == "" {
		return fmt.Errorf("业务类型不能为空")
	}
	if len(req.OrderLines) == 0 {
		return fmt.Errorf("入库明细不能为空")
	}

	// 验证明细
	for i, line := range req.OrderLines {
		if line.ItemCode == "" {
			return fmt.Errorf("第%d行商品编码不能为空", i+1)
		}
		if line.ItemName == "" {
			return fmt.Errorf("第%d行商品名称不能为空", i+1)
		}
		if line.PlanQty <= 0 {
			return fmt.Errorf("第%d行计划数量必须大于0", i+1)
		}
	}

	return nil
}

// validateEntryOrderConfirmRequest 验证确认入库请求
func (c *Client) validateEntryOrderConfirmRequest(req *EntryOrderConfirmRequest) error {
	if req == nil {
		return fmt.Errorf("请求参数不能为空")
	}
	if req.EntryOrderCode == "" {
		return fmt.Errorf("入库单号不能为空")
	}
	if req.WarehouseCode == "" {
		return fmt.Errorf("仓库编码不能为空")
	}
	if req.OwnerCode == "" {
		return fmt.Errorf("货主编码不能为空")
	}
	if len(req.OrderLines) == 0 {
		return fmt.Errorf("确认明细不能为空")
	}

	// 验证明细
	for i, line := range req.OrderLines {
		if line.ItemCode == "" {
			return fmt.Errorf("第%d行商品编码不能为空", i+1)
		}
		if line.ActualQty < 0 {
			return fmt.Errorf("第%d行实际数量不能小于0", i+1)
		}
	}

	return nil
}

// ConvertToQimenEntryOrderCreateRequest 转换为奇门入库单创建请求
func ConvertToQimenEntryOrderCreateRequest(entryOrder interface{}, orderLines interface{}) (*EntryOrderCreateRequest, error) {
	// 这里需要根据实际的数据模型进行转换
	// 暂时返回一个示例实现
	
	entryOrderBytes, err := json.Marshal(entryOrder)
	if err != nil {
		return nil, fmt.Errorf("序列化入库单失败: %w", err)
	}

	orderLinesBytes, err := json.Marshal(orderLines)
	if err != nil {
		return nil, fmt.Errorf("序列化入库明细失败: %w", err)
	}

	// 这里应该根据实际的数据结构进行转换
	// 示例代码，需要根据实际情况修改
	var req EntryOrderCreateRequest
	if err := json.Unmarshal(entryOrderBytes, &req); err != nil {
		return nil, fmt.Errorf("反序列化入库单失败: %w", err)
	}

	var lines []EntryOrderLineRequest
	if err := json.Unmarshal(orderLinesBytes, &lines); err != nil {
		return nil, fmt.Errorf("反序列化入库明细失败: %w", err)
	}

	req.OrderLines = lines
	return &req, nil
}
