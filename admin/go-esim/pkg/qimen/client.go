package qimen

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/Wenpiner/iot-api/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"
)

// Client 奇门API客户端
type Client struct {
	httpClient *http.Client
	logger     logx.Logger
}

// NewClient 创建奇门API客户端
func NewClient() *Client {
	return &Client{
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		logger: logx.WithContext(context.Background()),
	}
}

// WarehouseConfig 云仓配置
type WarehouseConfig struct {
	ApiEndpoint string `json:"api_endpoint"`
	AppKey      string `json:"app_key"`
	AppSecret   string `json:"app_secret"`
	CustomerID  string `json:"customer_id"`
}

// BaseRequest 基础请求参数
type BaseRequest struct {
	Method     string `json:"method"`
	AppKey     string `json:"app_key"`
	Timestamp  string `json:"timestamp"`
	Format     string `json:"format"`
	Version    string `json:"version"`
	Sign       string `json:"sign"`
	CustomerID string `json:"customerId"`
}

// BaseResponse 基础响应
type BaseResponse struct {
	Code      string `json:"code"`
	Message   string `json:"message"`
	Success   bool   `json:"success"`
	RequestID string `json:"requestId"`
}

// generateSign 生成签名
func (c *Client) generateSign(params map[string]interface{}, appSecret string) string {
	// 1. 参数排序
	keys := make([]string, 0, len(params))
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 2. 拼接参数
	var buffer bytes.Buffer
	buffer.WriteString(appSecret)
	for _, k := range keys {
		if v := params[k]; v != nil && v != "" {
			buffer.WriteString(k)
			buffer.WriteString(fmt.Sprintf("%v", v))
		}
	}
	buffer.WriteString(appSecret)

	// 3. MD5加密并转大写
	hash := md5.Sum(buffer.Bytes())
	return strings.ToUpper(fmt.Sprintf("%x", hash))
}

// buildRequest 构建请求
func (c *Client) buildRequest(method string, config *WarehouseConfig, bizContent interface{}) (map[string]interface{}, error) {
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)

	// 序列化业务参数
	bizContentBytes, err := json.Marshal(bizContent)
	if err != nil {
		return nil, fmt.Errorf("序列化业务参数失败: %w", err)
	}

	params := map[string]interface{}{
		"method":     method,
		"app_key":    config.AppKey,
		"timestamp":  timestamp,
		"format":     "json",
		"version":    "2.0",
		"customerId": config.CustomerID,
	}

	// 只有在有业务内容时才添加
	if len(bizContentBytes) > 2 { // 不是空的 "{}"
		params["biz_content"] = string(bizContentBytes)
	}

	// 生成签名
	sign := c.generateSign(params, config.AppSecret)
	params["sign"] = sign

	return params, nil
}

// doRequest 执行HTTP请求
func (c *Client) doRequest(ctx context.Context, config *WarehouseConfig, params map[string]interface{}) ([]byte, error) {
	// 构建POST表单数据
	formData := url.Values{}
	for k, v := range params {
		formData.Set(k, fmt.Sprintf("%v", v))
	}

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "POST", config.ApiEndpoint, strings.NewReader(formData.Encode()))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("User-Agent", "NIUYI-WMS/1.0")

	// 记录请求日志
	c.logger.Infof("奇门API请求: %s, 参数: %+v", config.ApiEndpoint, params)

	// 发送请求
	startTime := time.Now()
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 记录响应时间
	responseTime := time.Since(startTime).Milliseconds()
	c.logger.Infof("奇门API响应时间: %dms", responseTime)

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP状态码错误: %d, 响应: %s", resp.StatusCode, string(body))
	}

	c.logger.Infof("奇门API响应: %s", string(body))
	return body, nil
}

// CallAPI 调用奇门API
func (c *Client) CallAPI(ctx context.Context, method string, config *WarehouseConfig, bizContent interface{}) ([]byte, error) {
	// 构建请求参数
	params, err := c.buildRequest(method, config, bizContent)
	if err != nil {
		return nil, err
	}

	// 执行请求
	return c.doRequest(ctx, config, params)
}

// CallAPIWithRetry 带重试的API调用
func (c *Client) CallAPIWithRetry(ctx context.Context, method string, config *WarehouseConfig, bizContent interface{}, maxRetry int) ([]byte, error) {
	var lastErr error

	for i := 0; i <= maxRetry; i++ {
		if i > 0 {
			// 指数退避重试
			delay := time.Duration(i*i) * time.Second
			if delay > 30*time.Second {
				delay = 30 * time.Second
			}

			c.logger.Infof("奇门API重试第%d次，延迟%v", i, delay)
			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(delay):
			}
		}

		result, err := c.CallAPI(ctx, method, config, bizContent)
		if err == nil {
			return result, nil
		}

		lastErr = err
		c.logger.Errorf("奇门API调用失败(第%d次): %v", i+1, err)
	}

	return nil, fmt.Errorf("奇门API调用失败，已重试%d次: %w", maxRetry, lastErr)
}

// ParseResponse 解析响应
func (c *Client) ParseResponse(responseBody []byte, result interface{}) error {
	// 先解析基础响应
	var baseResp BaseResponse
	if err := json.Unmarshal(responseBody, &baseResp); err != nil {
		return fmt.Errorf("解析基础响应失败: %w", err)
	}

	// 检查业务状态
	if !baseResp.Success {
		return errors.ErrQimenApiError.WithDetail(fmt.Sprintf("code: %s, message: %s", baseResp.Code, baseResp.Message))
	}

	// 解析具体结果
	if result != nil {
		if err := json.Unmarshal(responseBody, result); err != nil {
			return fmt.Errorf("解析业务响应失败: %w", err)
		}
	}

	return nil
}

// ValidateConfig 验证配置
func (c *Client) ValidateConfig(config *WarehouseConfig) error {
	if config == nil {
		return fmt.Errorf("云仓配置不能为空")
	}
	if config.ApiEndpoint == "" {
		return fmt.Errorf("API接口地址不能为空")
	}
	if config.AppKey == "" {
		return fmt.Errorf("AppKey不能为空")
	}
	if config.AppSecret == "" {
		return fmt.Errorf("AppSecret不能为空")
	}
	if config.CustomerID == "" {
		return fmt.Errorf("客户ID不能为空")
	}
	return nil
}
