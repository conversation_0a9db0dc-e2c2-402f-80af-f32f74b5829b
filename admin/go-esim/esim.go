//	esim
//
//	Description: esim service
//
//	Schemes: http, https
//	Host: localhost:8081
//	BasePath: /
//	Version: 0.0.1
//	SecurityDefinitions:
//	  Token:
//	    type: apiKey
//	    name: Authorization
//	    in: header
//	Security:
//	  Token:
//	Consumes:
//	  - application/json
//
//	Produces:
//	  - application/json
//
// swagger:meta
package main

import (
	"context"
	"flag"
	"fmt"

	"github.com/Wenpiner/iot-api/internal/config"
	"github.com/Wenpiner/iot-api/internal/handler"
	"github.com/Wenpiner/iot-api/internal/svc"
	"github.com/robfig/cron/v3"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/rest"
)

var configFile = flag.String("f", "etc/esim.yaml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c, conf.UseEnv())

	sg := service.NewServiceGroup()

	server := rest.MustNewServer(c.RestConf, rest.WithCors(c.CROSConf.Address))
	defer server.Stop()

	ctx2 := context.Background()
	db, err := svc.InitDB(ctx2, c)
	if err != nil {
		panic(err)
	}
	defer db.Close()

	redisClient, err := svc.InitRedis(ctx2, c)
	if err != nil {
		panic(err)
	}
	defer redisClient.Close()

	ctx := svc.NewServiceContext(c, db, redisClient)
	handler.RegisterHandlers(server, ctx)
	sg.Add(ctx.RabbitMQ)
	sg.Add(server)
	

	// 创建一个定时任务，每5分钟执行一次
	c2 := cron.New()
	id, err := c2.AddFunc("@every 5m", ctx.StartTimeoutTask)
	if err != nil {
		panic(err)
	}
	logx.Infof("添加定时任务: %d", id)
	c2.Start()
	defer c2.Stop()

	defer sg.Stop()

	fmt.Printf("Starting server at %s:%d...\n", c.Host, c.Port)
	sg.Start()
}
