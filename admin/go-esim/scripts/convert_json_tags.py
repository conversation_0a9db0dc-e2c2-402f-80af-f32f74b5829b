#!/usr/bin/env python3
"""
脚本用于将 warehouse.api 文件中的 JSON tag 从 snake_case 转换为 camelCase
"""

import re
import sys

def snake_to_camel(snake_str):
    """将 snake_case 转换为 camelCase"""
    components = snake_str.split('_')
    return components[0] + ''.join(word.capitalize() for word in components[1:])

def convert_json_tags(content):
    """转换文件中的所有 JSON tag"""
    # 匹配 JSON tag 的正则表达式
    # 匹配形如 `json:"field_name"` 或 `json:"field_name,optional"` 的模式
    pattern = r'`json:"([a-z_]+)([^"]*?)"`'
    
    def replace_func(match):
        field_name = match.group(1)
        rest_of_tag = match.group(2)  # 包含 ,optional 等
        
        # 转换为驼峰命名
        camel_name = snake_to_camel(field_name)
        
        return f'`json:"{camel_name}{rest_of_tag}"`'
    
    # 执行替换
    converted_content = re.sub(pattern, replace_func, content)
    
    # 同样处理 form tag
    form_pattern = r'`form:"([a-z_]+)([^"]*?)"`'
    
    def replace_form_func(match):
        field_name = match.group(1)
        rest_of_tag = match.group(2)  # 包含 ,default=1 等
        
        # 转换为驼峰命名
        camel_name = snake_to_camel(field_name)
        
        return f'`form:"{camel_name}{rest_of_tag}"`'
    
    converted_content = re.sub(form_pattern, replace_form_func, converted_content)
    
    return converted_content

def main():
    # 读取文件
    file_path = '../desc/warehouse.api'
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 转换内容
        converted_content = convert_json_tags(content)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(converted_content)
        
        print("JSON tags 转换完成！")
        
    except FileNotFoundError:
        print(f"文件 {file_path} 不存在")
        sys.exit(1)
    except Exception as e:
        print(f"转换过程中出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
