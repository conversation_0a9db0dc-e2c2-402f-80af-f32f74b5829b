### Go template
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

*.gz
*.rar
*.tmp

# Other files and folders
.settings/
.idea/
.vscode/

# config file avoid exposing private data
*_dev.yaml

# Build files
*_api
*_rpc

# VsCode debug files
__debug*