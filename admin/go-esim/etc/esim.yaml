Name: Esim.api
Host: 0.0.0.0
Port: 8081
Timeout: 2000

Auth:
  AccessSecret: rbGxxKN#xo7&sK4TA^
  AccessExpire: 259200

CROSConf:
  Address: '*'

Log:
  ServiceName: EsimApiLogger
  Encoding: plain
  Stat: false


RedisConf:
  Host: 127.0.0.1:16379
  Pass: redis
  Db: 0

DatabaseConf:
  Type: postgres
  Host: 127.0.0.1
  Port: 25432
  DBName: postgres
  Username: postgres
  Password: postgres
  MaxOpenConn: 100
  SSLMode: disable
  CacheTime: 5

CasbinConf:
  ModelText: |
    [request_definition]
    r = sub, obj, act
    [policy_definition]
    p = sub, obj, act
    [role_definition]
    g = _, _
    [policy_effect]
    e = some(where (p.eft == allow))
    [matchers]
    m = r.sub == p.sub && keyMatch2(r.obj,p.obj) && r.act == p.act

RabbitMQConf:
  Host: 127.0.0.1
  Port: 5672
  Username: rabbitmq
  Password: 7WkmhmHYwEbmR7C7
  VHost: "/"

CoreRpc:
  Target: esim-go-core-rpc:9101
  Enabled: false
