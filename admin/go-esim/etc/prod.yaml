Name: Esim.api
Host: 0.0.0.0
Port: 8081
Timeout: 2000

Auth:
  AccessSecret: ${JWT_SECRET}
  AccessExpire: 259200

CROSConf:
  Address: '*'

Log:
  ServiceName: EsimApiLogger
  Encoding: plain
  Stat: false


RedisConf:
  Host: ${REDIS_HOST}
  Pass: ${REDIS_PASSWORD}
  Db: 0

DatabaseConf:
  Type: postgres
  Host: ${DB_HOST}
  Port: ${DB_PORT}
  DBName: ${DB_NAME}
  Username: ${DB_USERNAME}
  Password: ${DB_PASSWORD}
  MaxOpenConn: 100
  SSLMode: disable
  CacheTime: 5

CasbinConf:
  ModelText: |
    [request_definition]
    r = sub, obj, act
    [policy_definition]
    p = sub, obj, act
    [role_definition]
    g = _, _
    [policy_effect]
    e = some(where (p.eft == allow))
    [matchers]
    m = r.sub == p.sub && keyMatch2(r.obj,p.obj) && r.act == p.act

RabbitMQConf:
  Host: ${RABBITMQ_HOST}
  Port: ${RABBITMQ_PORT}
  Username: ${RABBITMQ_USER}
  Password: ${RABBITMQ_PASSWORD}
  VHost: "/"

CoreRpc:
  Target: esim-go-core-rpc:9101
  Enabled: true
